#!/bin/bash

# Check if Universal Control is disabled (returns 1)
is_disabled=$(defaults read com.apple.universalcontrol Disable 2>/dev/null)

# If it's disabled (1) or the setting doesn't exist, enable it
if [[ "$is_disabled" == "1" || -z "$is_disabled" ]]; then
    echo "Universal Control is disabled. Enabling it now..."
    defaults write com.apple.universalcontrol Disable -bool false
    echo "Universal Control has been enabled."
else
    echo "Universal Control is already enabled."
fi
