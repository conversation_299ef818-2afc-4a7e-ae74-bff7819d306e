//@version=6
indicator('Four EMAs Crossover', overlay = true, explicit_plot_zorder=true)

// Input variables
var int len1 = input.int(8, minval = 1, title = 'EMA1')
var int len2 = input.int(13, minval = 1, title = 'EMA2')
var int len3 = input.int(21, minval = 1, title = 'EMA3')
var int len4 = input.int(55, minval = 1, title = 'EMA4')

// Plot settings
show_ema_lines = input.bool(true, 'Show EMA lines')

// Calculate EMAs
float ema1 = ta.ema(close, len1)
float ema2 = ta.ema(close, len2)
float ema3 = ta.ema(close, len3)
float ema4 = ta.ema(close, len4)

// Plot EMAs
plot(show_ema_lines ? ema1 : na, title = 'EMA1', color = color.blue)
plot(show_ema_lines ? ema2 : na, title = 'EMA2', color = color.red)
plot(show_ema_lines ? ema3 : na, title = 'EMA3', color = color.green)
plot(show_ema_lines ? ema4 : na, title = 'EMA4', color = #2fff00)

// Define conditions
// bool longCond = ema1 > ema4 and ema2 > ema4 and ema3 > ema4
bool longCond = ema1 > ema2 and ema2 > ema3 and ema3 > ema4
// bool shortCond = ema1 < ema4 and ema2 < ema4 and ema3 < ema4
bool shortCond = ema1 < ema2 and ema2 < ema3 and ema3 < ema4

// Initialize condition state
var int CondIni = 0
CondIni := longCond ? 1 : shortCond ? -1 : CondIni[1]

// Define entry conditions
bool longCondition = longCond and CondIni[1] == -1
bool shortCondition = shortCond and CondIni[1] == 1

// Add EMA1 cross conditions
bool ema1_cross_ema2_dn = ta.crossunder(ema1, ema2)
bool ema1_cross_ema3_dn = ta.crossunder(ema1, ema3)
bool ema1_cross_ema4_dn = ta.crossunder(ema1, ema4)

bool ema1_cross_ema2_up = ta.crossover(ema1, ema2)
bool ema1_cross_ema3_up = ta.crossover(ema1, ema3)
bool ema1_cross_ema4_up = ta.crossover(ema1, ema4)

// Conditions for candle closing above/below EMA2
bool candle_close_below_ema2 = close < ema2 and longCond
bool candle_close_above_ema2 = close > ema2 and shortCond

// Plot signals
plotshape(longCondition, title = 'Buy Signal', text = 'B', textcolor = color.white, style = shape.labelup, size = size.tiny, location = location.belowbar, color = color.green)
plotshape(shortCondition, title = 'Short Signal', text = 'S', textcolor = color.white, style = shape.labeldown, size = size.tiny, location = location.abovebar, color = color.red)

// Plot EMA cross triangles with different sizes
plotshape(ema1_cross_ema2_dn, title = 'EMA1-EMA2 Crossdown', text = '2', textcolor = color.white, style = shape.labeldown, location = location.abovebar, color = color.red, size = size.tiny)
plotshape(ema1_cross_ema3_dn, title = 'EMA1-EMA3 Crossdown', text = '3', textcolor = color.white, style = shape.labeldown, location = location.abovebar, color = color.red, size = size.tiny)
plotshape(ema1_cross_ema4_dn, title = 'EMA1-EMA4 Crossdown', text = '4', textcolor = color.white, style = shape.labeldown, location = location.abovebar, color = color.red, size = size.tiny)

plotshape(ema1_cross_ema2_up, title = 'EMA1-EMA2 Crossup', text = '2', textcolor = color.white, style = shape.labelup, location = location.belowbar, color = color.green, size = size.tiny)
plotshape(ema1_cross_ema3_up, title = 'EMA1-EMA3 Crossup', text = '3', textcolor = color.white, style = shape.labelup, location = location.belowbar, color = color.green, size = size.tiny)
plotshape(ema1_cross_ema4_up, title = 'EMA1-EMA4 Crossup', text = '4', textcolor = color.white, style = shape.labelup, location = location.belowbar, color = color.green, size = size.tiny)

// Signals for candle closing above/below EMA2
plotshape(candle_close_below_ema2, title = 'Candle Close Below EMA2', text = '↓', textcolor = color.white, style = shape.labeldown, size = size.tiny, location = location.abovebar, color = color.red)
plotshape(candle_close_above_ema2, title = 'Candle Close Above EMA2', text = '↑', textcolor = color.white, style = shape.labelup, size = size.tiny, location = location.belowbar, color = color.green)

// Alert conditions for all triangles
alertcondition(longCondition, title = 'Four EMAs Buy Alert', message = 'Four EMAs BUY Signal')
alertcondition(shortCondition, title = 'Four EMAs Sell Alert', message = 'Four EMAs SELL Signal')

alertcondition(ema1_cross_ema2_dn, title = 'EMA1-EMA2 Crossdown Alert', message = 'EMA1 crossed below EMA2')
alertcondition(ema1_cross_ema3_dn, title = 'EMA1-EMA3 Crossdown Alert', message = 'EMA1 crossed below EMA3')
alertcondition(ema1_cross_ema4_dn, title = 'EMA1-EMA4 Crossdown Alert', message = 'EMA1 crossed below EMA4')

alertcondition(ema1_cross_ema2_up, title = 'EMA1-EMA2 Crossup Alert', message = 'EMA1 crossed above EMA2')
alertcondition(ema1_cross_ema3_up, title = 'EMA1-EMA3 Crossup Alert', message = 'EMA1 crossed above EMA3')
alertcondition(ema1_cross_ema4_up, title = 'EMA1-EMA4 Crossup Alert', message = 'EMA1 crossed above EMA4')

// Alerts for candle closing above/below EMA2
alertcondition(candle_close_below_ema2, title = 'Candle Close Below EMA2 Alert', message = 'Candle closed below EMA2')
alertcondition(candle_close_above_ema2, title = 'Candle Close Above EMA2 Alert', message = 'Candle closed above EMA2')