// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © girishptryambakee

//@version=6
indicator("Enhanced Kaufman Adaptive MA with BB", shorttitle="KAMA+BB Enhanced", overlay=true)

// Input variables with more lenient defaults
length = input.int(10, title="KAMA Base Length", minval=1, maxval=50)
fastLength = input.int(2, title="Fast EMA Length", minval=1, maxval=10)
slowLength = input.int(30, title="Slow EMA Length", minval=10, maxval=50)
src = input.source(close, title="Source")
highlight = input.bool(true, title="Highlight KAMA Direction?")
awaitBarConfirmation = input.bool(false, title="Wait for Bar Close?")

// BB Inputs with adjusted parameters
bb_length = input.int(20, title="BB Length")
bb_mult = input.float(2.0, title="BB StdDev")
bb_expansion_threshold = input.float(0.5, title="BB Expansion Threshold", minval=0.1, maxval=3.0, step=0.1)

// KAMA Calculations
change = ta.change(src)
mom = math.abs(ta.change(src, length))
volatility = math.sum(math.abs(change), length)

// Efficiency Ratio
er = volatility != 0.0 ? mom / volatility : 0.0

fastAlpha = 2.0 / (fastLength + 1.0)
slowAlpha = 2.0 / (slowLength + 1.0)

alpha = math.pow(er * (fastAlpha - slowAlpha) + slowAlpha, 2)

// KAMA calculation
var float kama = na
kama := na(kama[1]) ? src : alpha * src + (1.0 - alpha) * nz(kama[1])

// Color logic - Simplified
await = awaitBarConfirmation ? barstate.isconfirmed : true
maColor = highlight ? (kama > kama[1] and await ? color.green : color.red) : color.new(#6d1e7f, 0)

// Bollinger Bands Calculation
basis = ta.sma(src, bb_length)
dev = bb_mult * ta.stdev(src, bb_length)
upper = basis + dev
lower = basis - dev

// BB width calculations - Simplified
bb_width = (upper - lower) / basis
bb_width_change = ta.roc(bb_width, 1)

// Plotting
plot(kama, title="KAMA", linewidth=2, color=maColor)
plot(upper, title="Upper BB", color=color.rgb(255, 255, 255, 50))
plot(basis, title="Middle BB", color=color.rgb(255, 255, 255, 50))
plot(lower, title="Lower BB", color=color.rgb(255, 255, 255, 50))

// Simplified signal detection
var colorChangeBar = 0
if maColor != maColor[1]
    colorChangeBar := bar_index

// More lenient signal conditions
validBuySignal = maColor == color.green and
                 maColor[1] == color.red and
                 close > kama and
                 close > basis

validSellSignal = maColor == color.red and
                  maColor[1] == color.green and
                  close < kama and
                  close < basis

// Signal visualization with updated sizes
plotshape(validBuySignal, title = 'Buy Signal', text = 'B', textcolor = color.white, style = shape.labelup, size = size.tiny, location = location.belowbar, color = color.green)
plotshape(validSellSignal, title = 'Short Signal', text = 'S', textcolor = color.white, style = shape.labeldown, size = size.tiny, location = location.abovebar, color = color.red)

// Plot arrows with updated sizes
plotshape(validBuySignal, title="Buy Signal", location=location.belowbar,
          color=color.green, style=shape.triangleup, size=size.tiny)
plotshape(validSellSignal, title="Sell Signal", location=location.abovebar,
          color=color.red, style=shape.triangledown, size=size.tiny)

// Alert conditions
alertcondition(validBuySignal,
              title="Buy Signal",
              message="KAMA Buy Signal!")

alertcondition(validSellSignal,
              title="Sell Signal",
              message="KAMA Sell Signal!")