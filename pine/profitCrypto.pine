//@version=6
indicator('ProfitCrypto', shorttitle = 'Profit Crypto', overlay = true)

G_SCRIPT01   = '■ ' + ''

// === INPUTS ===
res                       = input.timeframe('15',  'TIMEFRAME', group ="NON REPAINT")
useRes                    = input(true,            'Use Alternate Signals')
intRes                    = input(10,               'Multiplier for Alernate Signals')
basisType                 = input.string('ALMA',   'MA Type: ', options=['TEMA', 'HullMA', 'ALMA'])
basisLen                  = input.int(50,           'MA Period', minval=1)
offsetSigma               = input.int(5,           'Offset for LSMA / Sigma for ALMA', minval=0)
offsetALMA                = input.float(2,      'Offset for ALMA', minval=0, step=0.01)
scolor                    = input(false,           'Show coloured Bars to indicate Trend?')
delayOffset               = input.int(0,           'Delay Open/Close MA', minval=0, step=1,
                  tooltip = 'Forces Non-Repainting')
tradeType                 = input.string('BOTH',   'What trades should be taken : ',
                  options = ['LONG', 'SHORT', 'BOTH', 'NONE'])
//=== /INPUTS ===
h                         = input(false,           'Signals for Heikin Ashi Candles')

//INDICATOR SETTINGS
swing_length              = input.int(10,          'Swing High/Low Length', group = 'Settings', minval = 1, maxval = 50)
history_of_demand_to_keep = input.int(20,          'History To Keep', minval = 5, maxval = 50)
box_width                 = input.float(2.5,       'Supply/Demand Box Width', group = 'Settings', minval = 1, maxval = 10, step = 0.5)

//INDICATOR VISUAL SETTINGS
show_zigzag               = input.bool(false,      'Show Zig Zag', group = 'Visual Settings', inline = '1')
show_price_action_labels  = input.bool(false,      'Show Price Action Labels', group = 'Visual Settings', inline = '2')

supply_color              = input.color(#00000000, 'Supply', group = 'Visual Settings', inline = '3')
supply_outline_color      = input.color(#00000000, 'Outline', group = 'Visual Settings', inline = '3')

demand_color              = input.color(#00000000, 'Demand', group = 'Visual Settings', inline = '4')
demand_outline_color      = input.color(#00000000, 'Outline', group = 'Visual Settings', inline = '4')

bos_label_color          = input.color(#00000000, 'BOS Label', group = 'Visual Settings', inline = '5')
poi_label_color          = input.color(#00000000, 'POI Label', group = 'Visual Settings', inline = '7')
poi_border_color         = input.color(#00000000, 'POI border', group = 'Visual Settings', inline = '7')
swing_type_color         = input.color(#00000000, 'Price Action Label', group = 'Visual Settings', inline = '8')
zigzag_color             = input.color(#00000000, 'Zig Zag', group = 'Visual Settings', inline = '9')

// Risk Management Inputs
G_RISK = '■ Risk Management'
i_lxLvlTP1   = input.float(0.2,  'Level TP1', group = G_RISK, tooltip = '(%) Exit Level')
i_lxQtyTP1   = input.float(80.0, 'Qty   TP1', group = G_RISK, tooltip = '(%) Adjust trade exit volume')
i_lxLvlTP2   = input.float(0.5,  'Level TP2', group = G_RISK, tooltip = '(%) Exit Level')
i_lxQtyTP2   = input.float(10.0, 'Qty   TP2', group = G_RISK, tooltip = '(%) Adjust trade exit volume')
i_lxLvlTP3   = input.float(7.0,  'Level TP3', group = G_RISK, tooltip = '(%) Exit Level')
i_lxQtyTP3   = input.float(2,    'Qty   TP3', group = G_RISK, tooltip = '(%) Adjust trade exit volume')
i_lxLvlSL    = input.float(0.5,  'Stop Loss', group = G_RISK, tooltip = '(%) Exit Level')

i_sxLvlTP1   = i_lxLvlTP1
i_sxQtyTP1   = i_lxQtyTP1
i_sxLvlTP2   = i_lxLvlTP2
i_sxQtyTP2   = i_lxQtyTP2
i_sxLvlTP3   = i_lxLvlTP3
i_sxQtyTP3   = i_lxQtyTP3
i_sxLvlSL    = i_lxLvlSL

// Display Settings
G_DISPLAY    = 'Display'
i_alertOn    = input.bool(true, 'Alert Labels On/Off', group = G_DISPLAY)
i_barColOn   = input.bool(true, 'Bar Color On/Off', group = G_DISPLAY)

// Functions
variant(type, src, len, offSig, offALMA) =>
    v1    = ta.sma(src, len)  // Simple
    v2    = ta.ema(src, len)  // Exponential
    v3    = 2 * v2 - ta.ema(v2, len)  // Double Exponential
    v4    = 3 * (v2 - ta.ema(v2, len)) + ta.ema(ta.ema(v2, len), len)  // Triple Exponential
    v5    = ta.wma(src, len)  // Weighted
    v6    = ta.vwma(src, len)  // Volume Weighted
    v7    = 0.0
    sma_1 = ta.sma(src, len)  // Smoothed
    v7    := na(v7[1]) ? sma_1 : (v7[1] * (len - 1) + src) / len
    v8    = ta.wma(2 * ta.wma(src, len / 2) - ta.wma(src, len), math.round(math.sqrt(len)))  // Hull
    v9    = ta.linreg(src, len, offSig)  // Least Squares
    v10   = ta.alma(src, len, offALMA, offSig)  // Arnaud Legoux
    v11   = ta.sma(v1, len)  // Triangular (extreme smooth)

    // SuperSmoother filter
    a1    = math.exp(-1.414 * math.pi / len)
    b1    = 2 * a1 * math.cos(1.414 * math.pi / len)
    c2    = b1
    c3    = -a1 * a1
    c1    = 1 - c2 - c3
    v12   = 0.0
    v12  := c1 * (src + nz(src[1])) / 2 + c2 * nz(v12[1]) + c3 * nz(v12[2])

    type == 'EMA' ? v2 :
         type == 'DEMA' ? v3 :
         type == 'TEMA' ? v4 :
         type == 'WMA' ? v5 :
         type == 'VWMA' ? v6 :
         type == 'SMMA' ? v7 :
         type == 'HullMA' ? v8 :
         type == 'LSMA' ? v9 :
         type == 'ALMA' ? v10 :
         type == 'TMA' ? v11 :
         type == 'SSMA' ? v12 : v1

// Security wrapper
reso(exp, use, res) =>
    request.security(syminfo.tickerid, res, exp, gaps = barmerge.gaps_off, lookahead = barmerge.lookahead_on)

// Series Setup
src = h ? request.security(ticker.heikinashi(syminfo.tickerid), timeframe.period, close, lookahead = barmerge.lookahead_off) : close
closeSeries = variant(basisType, close[delayOffset], basisLen, offsetSigma, offsetALMA)
openSeries  = variant(basisType, open[delayOffset],  basisLen, offsetSigma, offsetALMA)

// Get Alternate resolution Series if selected
stratRes = switch
    timeframe.ismonthly  => str.tostring(timeframe.multiplier * intRes, '###M')
    timeframe.isweekly   => str.tostring(timeframe.multiplier * intRes, '###W')
    timeframe.isdaily    => str.tostring(timeframe.multiplier * intRes, '###D')
    timeframe.isintraday => str.tostring(timeframe.multiplier * intRes, '####')
    => '60'

closeSeriesAlt = reso(closeSeries, useRes, stratRes)
openSeriesAlt  = reso(openSeries, useRes, stratRes)

// Triggers
leTrigger    = ta.crossover (closeSeriesAlt, openSeriesAlt)
seTrigger    = ta.crossunder(closeSeriesAlt, openSeriesAlt)

// Calculate entry points and levels
float condition = na
condition := leTrigger and nz(condition[1]) <= 0.0 ? 1.0 :
             seTrigger and nz(condition[1]) >= 0.0 ? -1.0 : nz(condition[1])

// Plot signals
plotshape(i_alertOn and leTrigger, title = 'Long', text = 'Long', textcolor = color.white, color = color.green, style = shape.labelup, size = size.tiny, location = location.belowbar)
plotshape(i_alertOn and seTrigger, title = 'Short', text = 'Short', textcolor = color.white, color = color.red, style = shape.labeldown, size = size.tiny, location = location.abovebar)

// Plotting MA
plot(closeSeries, title = 'Close MA', color = color.blue, linewidth = 2)
plot(openSeries, title = 'Open MA', color = color.red, linewidth = 2)

// Bar coloring if enabled
barcolor(i_barColOn ? (condition > 0 ? color.green : condition < 0 ? color.red : na) : na)

// Alert conditions
alertcondition(leTrigger, title = 'Long Entry', message = 'Long Entry Signal')
alertcondition(seTrigger, title = 'Short Entry', message = 'Short Entry Signal')