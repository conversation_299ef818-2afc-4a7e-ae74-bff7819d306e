//@version=5
indicator(title='Super trend V with Alerts', overlay=true)

source = close
hilow = (high - low) * 100
openclose = (close - open) * 100
vol = volume / hilow
spreadvol = openclose * vol
VPT = spreadvol + ta.cum(spreadvol)
window_len = 28

v_len = 14
price_spread = ta.stdev(high - low, window_len)

v = spreadvol + ta.cum(spreadvol)
smooth = ta.sma(v, v_len)
v_spread = ta.stdev(v - smooth, window_len)
shadow = (v - smooth) / v_spread * price_spread

out = shadow > 0 ? high + shadow : low + shadow
//
src = out
src1 = open
src2 = low
src3 = high

tf = input(720, title='Timeframe')
len = math.min(4999, timeframe.isintraday and timeframe.multiplier >= 1 ? tf / timeframe.multiplier * 7 : timeframe.isintraday and timeframe.multiplier < 60 ? 60 / timeframe.multiplier * 24 * 7 : 7)

c = ta.ema(src, len)
plot(c, color=color.new(color.red, 0))
o = ta.ema(src1, len)
plot(o, color=color.new(color.blue, 0))
//h = ema(src3,len)
//l=ema(src2,len)
//
col = c > o ? color.lime : color.orange
vis = true
vl = c
ll = o
m1 = plot(vl, color=col, linewidth=1, transp=60)
m2 = plot(vis ? ll : na, color=col, linewidth=2, transp=80)

fill(m1, m2, color=col, transp=70)
//

vpt = ta.ema(out, len)

// INPUTS //
st_mult = input.float(1, title='SuperTrend Multiplier', minval=0, maxval=100, step=0.01)
st_period = input.int(10, title='SuperTrend Period', minval=1)

// CALCULATIONS //
up_lev = vpt - st_mult * ta.atr(st_period)
dn_lev = vpt + st_mult * ta.atr(st_period)

up_trend = 0.0
up_trend := close[1] > up_trend[1] ? math.max(up_lev, up_trend[1]) : up_lev

down_trend = 0.0
down_trend := close[1] < down_trend[1] ? math.min(dn_lev, down_trend[1]) : dn_lev

// Calculate trend var
trend = 0
trend := close > down_trend[1] ? 1 : close < up_trend[1] ? -1 : nz(trend[1], 1)

// Calculate SuperTrend Line
st_line = trend == 1 ? up_trend : down_trend

// Plotting
plot(st_line[1], color=trend == 1 ? color.green : color.red, style=plot.style_cross, linewidth=2, title='SuperTrend')
buy = ta.crossover(close, st_line) and close > o
sell = ta.crossunder(close, st_line) and close < o
//plotshape(crossover( close, st_line), location = location.belowbar, color = color.green,size=size.tiny)
//plotshape(crossunder(close, st_line), location = location.abovebar, color = color.red,size=size.tiny)
plotshape(buy, title='buy', text='Buy', color=color.new(color.green, 0), style=shape.labelup, location=location.belowbar, size=size.small, textcolor=color.new(color.white, 0))  //plot for buy icon
plotshape(sell, title='sell', text='Sell', color=color.new(color.red, 0), style=shape.labeldown, location=location.abovebar, size=size.small, textcolor=color.new(color.white, 0))  //plot for sell icon


//
multiplier = input.float(title='TP', defval=2, minval=1)
src5 = close
len5 = input.int(title='TP length', defval=150, minval=1)
offset = 0

calcSlope(src5, len5) =>
    sumX = 0.0
    sumY = 0.0
    sumXSqr = 0.0
    sumXY = 0.0
    for i = 1 to len5 by 1
        val = src5[len5 - i]
        per = i + 1.0
        sumX += per
        sumY += val
        sumXSqr += per * per
        sumXY += val * per
        sumXY


    slope = (len5 * sumXY - sumX * sumY) / (len5 * sumXSqr - sumX * sumX)
    average = sumY / len5
    intercept = average - slope * sumX / len5 + slope
    [slope, average, intercept]

var float tmp = na
[s, a, i] = calcSlope(src5, len5)

vwap1 = i + s * (len5 - offset)
sdev = ta.stdev(close, len5)
dev = multiplier * sdev
top = vwap1 + dev
bott = vwap1 - dev

//
z1 = vwap1 + dev
x1 = vwap1 - dev

low1 = ta.crossover(close, x1)
high1 = ta.crossunder(close, z1)

plotshape(low1, title='low', text='TP', color=color.new(color.red, 0), style=shape.labelup, location=location.belowbar, size=size.small, textcolor=color.new(color.white, 0))  //plot for buy icon
plotshape(high1, title='high', text='TP', color=color.new(color.green, 0), style=shape.labeldown, location=location.abovebar, size=size.small, textcolor=color.new(color.white, 0))  //plot for sell icon


/////// Alerts /////
alertcondition(buy, title='buy')
alertcondition(sell, title='sell')
alertcondition(low1, title='sell tp')
alertcondition(high1, title='buy tp')

