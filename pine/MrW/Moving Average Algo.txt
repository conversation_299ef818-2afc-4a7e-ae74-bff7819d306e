//@version=5
indicator(shorttitle='Moving Average Algo', title='Moving Average Algo', overlay=true, timeframe="", timeframe_gaps=true)
import Fontiramisu/fontilab/7 as fontilab 

UseTM = input(true, title="Use TM Filter")

// ] ------------------ MACD ---------------- [
// Getting inputs
fast_length = input(title="Fast Length", defval=12, group="MACD 30 9")
slow_length = input(title="Slow Length", defval=26, group="MACD 30 9")
src = input(title="Source", defval=close, group="MACD 30 9")
signal_length = input.int(title="Signal Smoothing",  minval = 1, maxval = 50, defval = 9, group="MACD 30 9")
sma_source = input.string(title="Oscillator MA Type",  defval="EMA", options=["SMA", "EMA"], group="MACD 30 9")
sma_signal = input.string(title="Signal Line MA Type", defval="EMA", options=["SMA", "EMA"], group="MACD 30 9")
// Plot colors
col_macd = input(#2962FF, "MACD Line  ", inline="MACD", group="MACD 30 9")
col_signal = input(#FF6D00, "Signal Line  ", inline="Signal", group="MACD 30 9")

// Calculating
fast_ma = sma_source == "SMA" ? ta.sma(src, fast_length) : ta.ema(src, fast_length)
slow_ma = sma_source == "SMA" ? ta.sma(src, slow_length) : ta.ema(src, slow_length)
macd = fast_ma - slow_ma
signal = sma_signal == "SMA" ? ta.sma(macd, signal_length) : ta.ema(macd, signal_length)
hist = macd - signal


//plot(hist, title="Histogram", style=plot.style_histogram, color=color.new(color.yellow,30))
//plot(hist, title="Line", color=color.aqua, linewidth=2, display=display.all)
//plot(macd, title="MACD", color=col_macd, display=display.none)
//plot(signal, title="Signal", color=col_signal, display=display.none)


// ] -------------- Divergence Handle -------------- [
showDiv = input.bool(title="Show Divergence", defval=true, group="div settings")
lbR = input(title="Pivot Lookback Right", defval=5, group="div settings")
lbL = input(title="Pivot Lookback Left", defval=5, group="div settings")
rangeUpper = input(title="Max of Lookback Range", defval=60, group="div settings")
rangeLower = input(title="Min of Lookback Range", defval=5, group="div settings")



plotBull = true //input(title="Plot Bullish", defval=true, group="div settings")
plotBullPot = true //input(title="Plot Bullish Potential", defval=true, group="div settings")
plotHiddenBull = false //input(title="Plot Hidden Bullish", defval=false, group="div settings")
plotHiddenBullPot = false //input(title="Plot Hidden Bullish", defval=false, group="div settings")
plotBear = true //input(title="Plot Bearish", defval=true, group="div settings")
plotBearPot = true //input(title="Plot Bearish Potential Potential", defval=true, group="div settings")
plotHiddenBear = false //input(title="Plot Hidden Bearish", defval=false, group="div settings")
plotHiddenBearPot = false //input(title="Plot Hidden Bearish Potential", defval=false, group="div settings")
bearColor = color.red
bearPotColor = color.new(color.red, 20)
bullColor = color.green
bullPotColor = color.new(color.green, 20)
hiddenBullColor = color.new(color.green, 80)
hiddenBearColor = color.new(color.red, 80)
textColor = color.white
textColorDivPot = color.new(color.white, 20)
noneColor = color.new(color.white, 100)

float osc = hist 

// Get pivots.
[plFound, phFound, plFoundPot, phFoundPot] = fontilab.getOscPivots(osc, lbL, lbR)

// Div for curent ut.
[bullDiv, bullDivPot, hiddenBullDiv, hiddenBullDivPot, bearDiv, bearDivPot, hiddenBearDiv, hiddenBearDivPot] = 
     fontilab.plotDivergences(osc, lbR, plFound, phFound, plFoundPot, phFoundPot, rangeLower, rangeUpper)

//------
// Regular Bullish
plot(
     false and showDiv and plotBullPot and plFound ? osc[lbR] : na,
     offset=-lbR,
     title="Regular Bullish",
     linewidth=2,
     color=(bullDiv ? bullColor : noneColor)
     )

plotshape(
	 false and showDiv and plotBullPot and bullDivPot ? osc[1] : na,
	 offset= -1,
	 title="Regular Bullish Pot Label",
	 text="B",
	 style=shape.labelup,
	 location=location.absolute,
	 color=bullPotColor,
	 textcolor=textColorDivPot
	 )
	 
plotshape(
	 showDiv and plotBullPot and bullDiv ? osc[lbR] : na,
	 offset=-lbR,
	 title="Regular Bullish Label",
	 text=" Bull ",
	 style=shape.labelup,
	 location=location.belowbar,
	 color=bullColor,
	 textcolor=textColor
	 )


//------
// Hidden Bullish
plot(
	 false and showDiv and plotHiddenBull and plFound ? osc[lbR] : na,
	 offset=-lbR,
	 title="Hidden Bullish",
	 linewidth=2,
	 color=(hiddenBullDiv ? hiddenBullColor : noneColor)
	 )

plotshape(
	 false and showDiv and plotHiddenBullPot and hiddenBullDivPot ? osc[1] : na,
	 offset=-1,
	 title="Hidden Bullish Pot Label",
	 text="H",
	 style=shape.labelup,
	 location=location.absolute,
	 color=bullPotColor,
	 textcolor=textColorDivPot
	 )

plotshape(
	 false and showDiv and plotHiddenBull and hiddenBullDiv ? osc[lbR] : na,
	 offset=-lbR,
	 title="Hidden Bullish Label",
	 text=" H Bull ",
	 style=shape.labelup,
	 location=location.absolute,
	 color=bullColor,
	 textcolor=textColor
	 )

//------
// Regular Bearish
plot(
	 false and showDiv and plotBearPot and phFound ? osc[lbR] : na,
	 offset=-lbR,
	 title="Regular Bearish",
	 linewidth=2,
	 color=(bearDiv ? bearColor : noneColor)
	 )

plotshape(
	 false and showDiv and plotBearPot and bearDivPot ? osc[1] : na,
	 offset=-1,
	 title="Regular Bearish Pot Label",
	 text="B",
	 style=shape.labeldown,
	 location=location.absolute,
	 color=bearPotColor,
	 textcolor=textColorDivPot
	 )
	 
plotshape(
	 showDiv and plotBearPot and bearDiv ? osc[lbR] : na,
	 offset=-lbR,
	 title="Regular Bearish Label",
	 text=" Bear ",
	 style=shape.labeldown,
	 location=location.abovebar,
	 color=bearColor,
	 textcolor=textColor
	 )



//-----
// Hidden Bearish
plot(
	 false and showDiv and plotHiddenBear and phFound ? osc[lbR] : na,
	 offset=-lbR,
	 title="Hidden Bearish",
	 linewidth=2,
	 color=(hiddenBearDiv ? hiddenBearColor : noneColor)
	 )

plotshape(
	 false and showDiv and plotHiddenBearPot and hiddenBearDivPot ? osc[1] : na,
	 offset=-1,
	 title="Hidden Bearish Pot Label",
	 text="H",
	 style=shape.labeldown,
	 location=location.absolute,
	 color=bearPotColor,
	 textcolor=textColorDivPot
	 )
	 
plotshape(
	 false and showDiv and plotHiddenBear and hiddenBearDiv ? osc[lbR] : na,
	 offset=-lbR,
	 title="Hidden Bearish Label",
	 text=" H Bear ",
	 style=shape.labeldown,
	 location=location.absolute,
	 color=bearColor,
	 textcolor=textColor
	 )






//-----------------------------------------------------


length = input(20, title='BB Length')
mult = input(2.0, title='BB MultFactor')
lengthKC = input(20, title='KC Length')
multKC = input(1.5, title='KC MultFactor')

useTrueRange = input(true, title='Use TrueRange (KC)')

// Calculate BB
source = close
basis = ta.sma(source, length)
dev = multKC * ta.stdev(source, length)
upperBB = basis + dev
lowerBB = basis - dev

// Calculate KC
ma = ta.sma(source, lengthKC)
range_1 = useTrueRange ? ta.tr : high - low
rangema = ta.sma(range_1, lengthKC)
upperKC = ma + rangema * multKC
lowerKC = ma - rangema * multKC

sqzOn = lowerBB > lowerKC and upperBB < upperKC
sqzOff = lowerBB < lowerKC and upperBB > upperKC
noSqz = sqzOn == false and sqzOff == false

val = ta.linreg(source - math.avg(math.avg(ta.highest(high, lengthKC), ta.lowest(low, lengthKC)), ta.sma(close, lengthKC)), lengthKC, 0)

iff_1 = val > nz(val[1]) ? color.lime : color.green
iff_2 = val < nz(val[1]) ? color.red : color.maroon
bcolor = val > 0 ? iff_1 : iff_2
scolor = noSqz ? color.blue : sqzOn ? color.black : color.gray


//plot(val, color=bcolor, style=plot.style_histogram, linewidth=4)
//plot(0, color=scolor, style=plot.style_cross, linewidth=2)

period=input(20,"CCI period")
coeff=input(1,"ATR Multiplier")
AP=input(5,"ATR Period")
ATR=ta.sma(ta.tr,AP)
src1=input(close)
upT=low-ATR*coeff
downT=high+ATR*coeff
MagicTrend=0.0
MagicTrend := ta.cci(src1,period)>=0 ? (upT<nz(MagicTrend[1]) ? nz(MagicTrend[1]) : upT) : (downT>nz(MagicTrend[1]) ? nz(MagicTrend[1]) : downT)
color1= ta.cci(src1,period)>=0 ? #0022FC : #FC0400


plot(MagicTrend, color=color1, linewidth=3)
//--------------------------------------------------------------------

isFilterBuy = not UseTM or close > MagicTrend
isFilterSell = not UseTM or close < MagicTrend

isBuy = val > nz(val[1]) and val>0 and nz(val[1]) < 0 and close > open and isFilterBuy
isSell = val < nz(val[1]) and val<0 and nz(val[1]) > 0 and close < open and isFilterSell

plotshape(isBuy ? 1 : na, style=shape.labelup, location=location.belowbar, size=size.normal, color=color.green, text="Buy",textcolor=color.white)
plotshape(isSell  ? 1 : na, style=shape.labeldown, location=location.abovebar, size=size.normal, color=color.red, text="Sell",textcolor=color.white)

alertcondition(isBuy , "Buy Signal", "Buy Signal")
alertcondition(isSell , "Sell Signal", "Sell Signal")