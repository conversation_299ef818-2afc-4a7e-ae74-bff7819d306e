//@version=4
study("MR WOW Confirmation", shorttitle="MR WOW CONFIRMATION",overlay=true)
length = input(24)
src = input(hl2)
showcross = input(true, "Show cross over/under")

hma(_src, _length)=>
    wma((2 * wma(_src, _length / 2)) - wma(_src, _length), round(sqrt(_length)))
    
hma3(_src, _length)=>
    p = length/2
    wma(wma(close,p/3)*3 - wma(close,p/2) - wma(close,p),p)


a = hma(src, length)
b = hma3(src, length)
c = b > a ? color.lime : color.red
p1 = plot(a,color=c,linewidth=1,transp=75)
p2 = plot(b,color=c,linewidth=1,transp=75)
fill(p1,p2,color=c,transp=55)
crossdn = a > b and a[1] < b[1]
crossup = b > a and b[1] < a[1]
plotshape(showcross and crossdn ? a : na, location=location.absolute, style=shape.labeldown, color=color.red, size=size.tiny, text="Sell", textcolor=color.white, transp=0, offset=-1)
plotshape(showcross and crossup ? a : na, location=location.absolute, style=shape.labelup, color=color.green, size=size.tiny, text="Buy", textcolor=color.white, transp=0, offset=-1)