//@version=6
indicator("Price action Trends", overlay = true, max_labels_count = 500, max_boxes_count = 500)

// ＩＮＰＵＴＳ ――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――{
var int short_len     = input.int(50, "Short Length")
var int long_len      = input.int(150, "Long Length")
var bool retest_sig   = input.bool(false, "Retest Signals")
var bool candle_color = input.bool(true, "Candle Color")

var color upper_col = input.color(#13bd6e, "Up Trend", inline = "colors")
var color lower_col = input.color(#af0d4b, "Down Trend", inline = "colors")

// Alert Inputs
var string group_alerts = "Alerts"
var bool alert_on_trend_change = input.bool(true, "Alert on Trend Change", group = group_alerts)
var string alert_message = input.string("Trend changed to: ", "Alert Message Prefix", group = group_alerts)


// ＣＡＬＣＵＬＡＴＩＯＮＳ――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――{
var float atr = ta.atr(200) * 0.5

var box lower_box = na
var box upper_box = na

// Price Action filter function
price_action_filter(float src, int length, float R = 0.01, float Q = 0.1) =>
    var float estimate = na
    var float error_est = 1.0
    var float error_meas = R * float(length)
    var float price_action_gain = 0.0
    var float prediction = na

    if na(estimate)
        estimate := src[1]

    prediction := estimate
    price_action_gain := error_est / (error_est + error_meas)
    estimate := prediction + price_action_gain * (src - prediction)
    error_est := (1 - price_action_gain) * error_est  + Q / float(length)

    estimate

var float short_price_action = price_action_filter(close, short_len)
var float long_price_action = price_action_filter(close, long_len)

var bool trend_up = short_price_action > long_price_action

var color trend_col  = trend_up ? upper_col : lower_col
var color trend_col1 = short_price_action > short_price_action[2] ? upper_col : lower_col
var color candle_col = candle_color ? (trend_up and short_price_action > short_price_action[2] ? upper_col : not trend_up and short_price_action < short_price_action[2] ? lower_col : color.gray) : na


// ALERTS ――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――{
// Alert conditions for easy setup
alertcondition(trend_up and not trend_up[1], "Trend Up", "Price Action Trend changed to UP")
alertcondition(trend_up[1] and not trend_up, "Trend Down", "Price Action Trend changed to DOWN")

// Legacy alerts with detailed information
if alert_on_trend_change
    if trend_up and not trend_up[1]
        alert(alert_message + "UP (Short: " + str.tostring(math.round(short_price_action,2)) + ", Long: " + str.tostring(math.round(long_price_action,2)) + ")", alert.freq_once_per_bar)
    if trend_up[1] and not trend_up
        alert(alert_message + "DOWN (Short: " + str.tostring(math.round(short_price_action,2)) + ", Long: " + str.tostring(math.round(long_price_action,2)) + ")", alert.freq_once_per_bar)

// ＰＬＯＴ ――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――{
if trend_up and not trend_up[1]
    label.new(bar_index, short_price_action, "🡹\n" + str.tostring(math.round(close, 1)), color = na, textcolor = upper_col, style = label.style_label_up, size = size.normal)
    lower_box := box.new(bar_index, low + atr, bar_index, low, border_color = na, bgcolor = color.new(upper_col, 60))

if not ta.change(trend_up)
    box.set_right(lower_box, bar_index)

if trend_up[1] and not trend_up
    label.new(bar_index, short_price_action, str.tostring(math.round(close, 1)) + "\n🢃", color = na, textcolor = lower_col, style = label.style_label_down, size = size.normal)
    upper_box := box.new(bar_index, high, bar_index, high - atr, border_color = na, bgcolor = color.new(lower_col, 60))

if not ta.change(trend_up)
    box.set_right(upper_box, bar_index)

if retest_sig
    if high < box.get_bottom(upper_box) and high[1] >= box.get_bottom(upper_box)
        label.new(bar_index-1, high[1], "x", color = na, textcolor = lower_col, style = label.style_label_down, size = size.normal)

    if low > box.get_top(lower_box) and low[1] <= box.get_top(lower_box)
        label.new(bar_index-1, low[1], "+", color = na, textcolor = upper_col, style = label.style_label_up, size = size.normal)

var p1 = plot(short_price_action, "Short Price Action", color = trend_col1)
var p2 = plot(long_price_action, "Long Price Action", linewidth = 2, color = trend_col)

fill(p1, p2, color = color.new(trend_col, 80))

plotcandle(open, high, low, close, title="Trend Candles", color = candle_col, wickcolor=candle_col, bordercolor = candle_col)