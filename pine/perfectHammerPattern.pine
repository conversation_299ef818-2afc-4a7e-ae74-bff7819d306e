//@version=6
indicator("Perfect Hammer Pattern", shorttitle="PerfectHammer", overlay=true)

// Add input for consecutive patterns
consecutivePatterns = input.int(2, "Consecutive Perfect Patterns", minval=1, maxval=10)

var float tolerance = 0.03
var float doji_tolerance = 0.001  // Tolerance for doji candlestick

is_doji(index) => math.abs(close[index] - open[index]) <= doji_tolerance

is_perfect_bullish(index) =>
     low[index] == open[index] and
     high[index] > close[index] and
     close[index] > open[index] and
     math.abs(low[index] - open[index]) <= tolerance and
     high[index] > close[index]

is_perfect_bearish(index) =>
     high[index] == open[index] and
     low[index] < close[index] and
     close[index] < open[index] and
     math.abs(high[index] - open[index]) <= tolerance and
     low[index] < close[index]

// Modified pattern detection for variable consecutive patterns
bullish_pattern = true
bearish_pattern = true

for i = 0 to consecutivePatterns - 1
    bullish_pattern := bullish_pattern and is_perfect_bullish(i)
    bearish_pattern := bearish_pattern and is_perfect_bearish(i)

// Add final condition for the pattern before the sequence
bullish_pattern := bullish_pattern and (close[consecutivePatterns] < open[consecutivePatterns] or is_doji(consecutivePatterns))
bearish_pattern := bearish_pattern and (close[consecutivePatterns] > open[consecutivePatterns] or is_doji(consecutivePatterns))

plotshape(bullish_pattern, title = 'Buy Signal', text = 'H', textcolor = color.white, style = shape.labelup, size = size.tiny, location = location.belowbar, color = color.green)
plotshape(bearish_pattern, title = 'Short Signal', text = 'H', textcolor = color.white, style = shape.labeldown, size = size.tiny, location = location.abovebar, color = color.red)

// Simple alert messages
alertcondition(bullish_pattern, title="Bullish Pattern Alert", message="Perfect Bullish Hammers Found!")
alertcondition(bearish_pattern, title="Bearish Pattern Alert", message="Perfect Bearish Hammers Found!")