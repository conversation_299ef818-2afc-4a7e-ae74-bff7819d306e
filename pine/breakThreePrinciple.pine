//@version=6
indicator("Break Three Principle", overlay=true)

// Input Parameters - keeping the sensitivity that worked well before
lookbackPeriod = input.int(15, "Main Lookback Period", minval=5, maxval=50)
testPeriod = input.int(8, "Test Detection Period", minval=3, maxval=20)
minPriceDistance = input.float(0.05, "Minimum Test Distance %", minval=0.02, maxval=1.0, step=0.01)

// Technical Settings that worked well in previous version
useAtr = input.bool(true, "Use ATR for Zone Width")
atrPeriod = input.int(10, "ATR Period", minval=5)
atrMultiplier = input.float(0.3, "ATR Zone Multiplier", minval=0.1, step=0.1)

// Volume Settings
useVolume = input.bool(true, "Use Volume Confirmation")
volumeMultiplier = input.float(0.8, "Volume Threshold", minval=0.5, maxval=2.0)

// Core calculations
var float prevHigh = high
var float prevLow = low
var int consecutiveHighs = 0
var int consecutiveLows = 0

// Calculate technical indicators - simplified for reliability
atr = ta.atr(atrPeriod)
highest = ta.highest(high, lookbackPeriod)
lowest = ta.lowest(low, lookbackPeriod)

// Calculate zones - keeping the effective zone width from before
float zoneWidth = useAtr ? atr * atrMultiplier : (highest - lowest) * 0.005
float upperZone = highest - zoneWidth
float lowerZone = lowest + zoneWidth

// Track resistance tests - maintaining previous logic that worked
if high > upperZone
    float highDist = math.abs(high - prevHigh) / prevHigh * 100
    if highDist >= minPriceDistance
        consecutiveHighs := consecutiveHighs + 1
        prevHigh := high
else if high < upperZone
    consecutiveHighs := 0
    prevHigh := high

// Track support tests - maintaining previous logic that worked
if low < lowerZone
    float lowDist = math.abs(low - prevLow) / prevLow * 100
    if lowDist >= minPriceDistance
        consecutiveLows := consecutiveLows + 1
        prevLow := low
else if low > lowerZone
    consecutiveLows := 0
    prevLow := low

// Reset conditions - keeping the effective reset logic
if high > highest + atr or low < lowest - atr
    consecutiveHighs := 0
    consecutiveLows := 0
    prevHigh := high
    prevLow := low

// Volume confirmation - simplified for better signal generation
float avgVolume = ta.sma(volume, 20)
bool volumeConfirm = not useVolume or volume > avgVolume * volumeMultiplier

// Generate signals - returning to the previous effective logic
resistanceSignal = consecutiveHighs >= 2 and
                  close < open and
                  high >= upperZone and
                  high < highest and
                  volumeConfirm

supportSignal = consecutiveLows >= 2 and
               close > open and
               low <= lowerZone and
               low > lowest and
               volumeConfirm

// Plot levels and zones
plot(highest, "Resistance", color=color.new(color.red, 0), linewidth=2)
plot(lowest, "Support", color=color.new(color.green, 0), linewidth=2)

// Plot signals with original size that worked well
plotshape(resistanceSignal, title="Failed Upward Breakout",
     style=shape.triangledown, location=location.abovebar,
     color=color.new(color.red, 0), size=size.small)

plotshape(supportSignal, title="Failed Downward Breakout",
     style=shape.triangleup, location=location.belowbar,
     color=color.new(color.green, 0), size=size.small)

// Information Table
var infoTable = table.new(position.top_right, 4, 3)

if barstate.islast
    table.cell(infoTable, 0, 0, "Metric", bgcolor=color.rgb(33, 33, 33))
    table.cell(infoTable, 1, 0, "Value", bgcolor=color.rgb(33, 33, 33))
    table.cell(infoTable, 2, 0, "Status", bgcolor=color.rgb(33, 33, 33))
    table.cell(infoTable, 3, 0, "Tests", bgcolor=color.rgb(33, 33, 33))

    table.cell(infoTable, 0, 1, "Resistance", text_color=color.red)
    table.cell(infoTable, 1, 1, str.tostring(highest, "#.##"), text_color=color.red)
    table.cell(infoTable, 2, 1, resistanceSignal ? "Active" : "Inactive")
    table.cell(infoTable, 3, 1, str.tostring(consecutiveHighs))

    table.cell(infoTable, 0, 2, "Support", text_color=color.green)
    table.cell(infoTable, 1, 2, str.tostring(lowest, "#.##"), text_color=color.green)
    table.cell(infoTable, 2, 2, supportSignal ? "Active" : "Inactive")
    table.cell(infoTable, 3, 2, str.tostring(consecutiveLows))

// Alert conditions - simplified for reliability
alertcondition(resistanceSignal, title="Failed Upward Breakout Alert",
     message="Resistance Rejection Detected")
alertcondition(supportSignal, title="Failed Downward Breakout Alert",
     message="Support Rejection Detected")