// @version=5
// Tripple SuperTrend for more streamlined alerts
// Showing VWAP to get understanding of trends flow by girishptryambakee

indicator(title="Triple Supertrend v1 w/ Alerts and Advanced VWAP", shorttitle="Triple Supertrend v1 + Adv VWAP", overlay=true)

//===== INPUTS ==========================================================================//

Factor1 = input.float(1, title="Factor 1", minval=0.1)
Pd1 = input.int(10, title="Period 1", minval=1, maxval=100)

Factor2 = input.float(2, title="Factor 2", minval=0.1)
Pd2 = input.int(20, title="Period 2", minval=1, maxval=100)

Factor3 = input.float(3, title="Factor 3", minval=0.1)
Pd3 = input.int(22, title="Period 3", minval=1, maxval=100)

// VWAP Settings
showVWAP = input.bool(true, title="Show VWAP")
hideonDWM = input(false, title="Hide VWAP on 1D or Above", group="VWAP Settings")
anchor = input.string(defval = "Session", title="Anchor Period",
 options=["Session", "Week", "Month", "Quarter", "Year", "Decade", "Century", "Earnings", "Dividends", "Splits"], group="VWAP Settings")
src = input(title = "Source", defval = hlc3, group="VWAP Settings")
offset = input.int(0, title="Offset", group="VWAP Settings", minval=0)

// VWAP Bands Settings
BANDS_GROUP = "Bands Settings"
CALC_MODE_TOOLTIP = "Determines the units used to calculate the distance of the bands. When 'Percentage' is selected, a multiplier of 1 means 1%."
calcModeInput = input.string("Standard Deviation", "Bands Calculation Mode", options = ["Standard Deviation", "Percentage"], group = BANDS_GROUP, tooltip = CALC_MODE_TOOLTIP)
showBand_1 = input(false, title = "", group = BANDS_GROUP, inline = "band_1")
bandMult_1 = input.float(1.0, title = "Bands Multiplier #1", group = BANDS_GROUP, inline = "band_1", step = 0.5, minval=0)
showBand_2 = input(false, title = "", group = BANDS_GROUP, inline = "band_2")
bandMult_2 = input.float(2.0, title = "Bands Multiplier #2", group = BANDS_GROUP, inline = "band_2", step = 0.5, minval=0)
showBand_3 = input(false, title = "", group = BANDS_GROUP, inline = "band_3")
bandMult_3 = input.float(3.0, title = "Bands Multiplier #3", group = BANDS_GROUP, inline = "band_3", step = 0.5, minval=0)

//===== FUNCTIONS =======================================================================//

supertrend(factor, pd) =>
    Up = hl2 - (factor * ta.atr(pd))
    Dn = hl2 + (factor * ta.atr(pd))
    TrendUp = 0.0
    TrendUp := close[1] > TrendUp[1] ? math.max(Up, TrendUp[1]) : Up
    TrendDown = 0.0
    TrendDown := close[1] < TrendDown[1] ? math.min(Dn, TrendDown[1]) : Dn
    Trend = 0
    Trend := close > TrendDown[1] ? 1 : close < TrendUp[1] ? -1 : nz(Trend[1], 1)
    Tsl = Trend == 1 ? TrendUp : TrendDown
    [Tsl, Trend]

//===== CALCULATIONS ====================================================================//

[Tsl1, Trend1] = supertrend(Factor1, Pd1)
[Tsl2, Trend2] = supertrend(Factor2, Pd2)
[Tsl3, Trend3] = supertrend(Factor3, Pd3)

// VWAP Calculations
if barstate.islast and ta.cum(volume) == 0
    runtime.error("No volume is provided by the data vendor.")

new_earnings = request.earnings(syminfo.tickerid, earnings.actual, barmerge.gaps_on, barmerge.lookahead_on, ignore_invalid_symbol=true)
new_dividends = request.dividends(syminfo.tickerid, dividends.gross, barmerge.gaps_on, barmerge.lookahead_on, ignore_invalid_symbol=true)
new_split = request.splits(syminfo.tickerid, splits.denominator, barmerge.gaps_on, barmerge.lookahead_on, ignore_invalid_symbol=true)

isNewPeriod = switch anchor
    "Earnings"  => not na(new_earnings)
    "Dividends" => not na(new_dividends)
    "Splits"    => not na(new_split)
    "Session"   => timeframe.change("D")
    "Week"      => timeframe.change("W")
    "Month"     => timeframe.change("M")
    "Quarter"   => timeframe.change("3M")
    "Year"      => timeframe.change("12M")
    "Decade"    => timeframe.change("12M") and year % 10 == 0
    "Century"   => timeframe.change("12M") and year % 100 == 0
    => false

isEsdAnchor = anchor == "Earnings" or anchor == "Dividends" or anchor == "Splits"
if na(src[1]) and not isEsdAnchor
    isNewPeriod := true

float vwapValue = na
float upperBandValue1 = na
float lowerBandValue1 = na
float upperBandValue2 = na
float lowerBandValue2 = na
float upperBandValue3 = na
float lowerBandValue3 = na

if not (hideonDWM and timeframe.isdwm)
    [_vwap, _stdevUpper, _] = ta.vwap(src, isNewPeriod, 1)
    vwapValue := _vwap
    stdevAbs = _stdevUpper - _vwap
    bandBasis = calcModeInput == "Standard Deviation" ? stdevAbs : _vwap * 0.01
    upperBandValue1 := _vwap + bandBasis * bandMult_1
    lowerBandValue1 := _vwap - bandBasis * bandMult_1
    upperBandValue2 := _vwap + bandBasis * bandMult_2
    lowerBandValue2 := _vwap - bandBasis * bandMult_2
    upperBandValue3 := _vwap + bandBasis * bandMult_3
    lowerBandValue3 := _vwap - bandBasis * bandMult_3

//===== PLOTS ===========================================================================//

plot(Tsl1, color=Trend1 == 1 ? #1B5E20 : #801922, linewidth=1, title="SuperTrend 1")
plot(Tsl2, color=Trend2 == 1 ? #66BB6A : #F7525F, linewidth=1, title="SuperTrend 2")
plot(Tsl3, color=Trend3 == 1 ? #A5D6A7 : #FAA1A4, linewidth=1, title="SuperTrend 3")

// Plot VWAP
plot(showVWAP ? vwapValue : na, title="VWAP", color=#FFFFFF, offset=offset)

upperBand_1 = plot(showVWAP and showBand_1 ? upperBandValue1 : na, title="Upper Band #1", color=#1B5E20, offset=offset)
lowerBand_1 = plot(showVWAP and showBand_1 ? lowerBandValue1 : na, title="Lower Band #1", color=#1B5E20, offset=offset)
fill(upperBand_1, lowerBand_1, title="Bands Fill #1", color=color.new(#1B5E20, 95))

upperBand_2 = plot(showVWAP and showBand_2 ? upperBandValue2 : na, title="Upper Band #2", color=#66BB6A, offset=offset)
lowerBand_2 = plot(showVWAP and showBand_2 ? lowerBandValue2 : na, title="Lower Band #2", color=#66BB6A, offset=offset)
fill(upperBand_2, lowerBand_2, title="Bands Fill #2", color=color.new(#66BB6A, 95))

upperBand_3 = plot(showVWAP and showBand_3 ? upperBandValue3 : na, title="Upper Band #3", color=#A5D6A7, offset=offset)
lowerBand_3 = plot(showVWAP and showBand_3 ? lowerBandValue3 : na, title="Lower Band #3", color=#A5D6A7, offset=offset)
fill(upperBand_3, lowerBand_3, title="Bands Fill #3", color=color.new(color.teal, 95))

// SuperTrend 1 Signals
plotshape(ta.cross(close, Tsl1) and close > Tsl1, title="Up Arrow 1", style=shape.triangleup, location=location.belowbar, color=#1B5E20, size=size.tiny)
plotshape(ta.cross(Tsl1, close) and close < Tsl1, title="Down Arrow 1", style=shape.triangledown, location=location.abovebar, color=color.red, size=size.tiny)
plotarrow(Trend1 == 1 and Trend1[1] == -1 ? 1 : na, title="Up Entry Arrow 1", colorup=#1B5E20, maxheight=50, minheight=40)
plotarrow(Trend1 == -1 and Trend1[1] == 1 ? -1 : na, title="Down Entry Arrow 1", colordown=#801922, maxheight=50, minheight=40)

// SuperTrend 2 Signals
plotshape(ta.cross(close, Tsl2) and close > Tsl2, title="Up Arrow 2", style=shape.triangleup, location=location.belowbar, color=#66BB6A, size=size.tiny)
plotshape(ta.cross(Tsl2, close) and close < Tsl2, title="Down Arrow 2", style=shape.triangledown, location=location.abovebar, color=#F7525F, size=size.tiny)
plotarrow(Trend2 == 1 and Trend2[1] == -1 ? 1 : na, title="Up Entry Arrow 2", colorup=#66BB6A, maxheight=50, minheight=40)
plotarrow(Trend2 == -1 and Trend2[1] == 1 ? -1 : na, title="Down Entry Arrow 2", colordown=#F7525F, maxheight=50, minheight=40)

// SuperTrend 3 Signals
plotshape(ta.cross(close, Tsl3) and close > Tsl3, title="Up Arrow 3", style=shape.triangleup, location=location.belowbar, color=#A5D6A7, size=size.tiny)
plotshape(ta.cross(Tsl3, close) and close < Tsl3, title="Down Arrow 3", style=shape.triangledown, location=location.abovebar, color=#FAA1A4, size=size.tiny)
plotarrow(Trend3 == 1 and Trend3[1] == -1 ? 1 : na, title="Up Entry Arrow 3", colorup=#A5D6A7, maxheight=50, minheight=40)
plotarrow(Trend3 == -1 and Trend3[1] == 1 ? -1 : na, title="Down Entry Arrow 3", colordown=#FAA1A4, maxheight=50, minheight=40)

//===== ALERTS ==========================================================================//

alertcondition(ta.cross(close, Tsl1) and close > Tsl1, title='Supertrend 1 Long', message='Supertrend 1: Long')
alertcondition(ta.cross(Tsl1, close) and close < Tsl1, title='Supertrend 1 Short', message='Supertrend 1: Short')
alertcondition(Trend1 == 1 and Trend1[1] == -1, title='Supertrend 1 Trend Change to Long', message='Supertrend 1: Trend Changed to Long')
alertcondition(Trend1 == -1 and Trend1[1] == 1, title='Supertrend 1 Trend Change to Short', message='Supertrend 1: Trend Changed to Short')

alertcondition(ta.cross(close, Tsl2) and close > Tsl2, title='Supertrend 2 Long', message='Supertrend 2: Long')
alertcondition(ta.cross(Tsl2, close) and close < Tsl2, title='Supertrend 2 Short', message='Supertrend 2: Short')
alertcondition(Trend2 == 1 and Trend2[1] == -1, title='Supertrend 2 Trend Change to Long', message='Supertrend 2: Trend Changed to Long')
alertcondition(Trend2 == -1 and Trend2[1] == 1, title='Supertrend 2 Trend Change to Short', message='Supertrend 2: Trend Changed to Short')

alertcondition(ta.cross(close, Tsl3) and close > Tsl3, title='Supertrend 3 Long', message='Supertrend 3: Long')
alertcondition(ta.cross(Tsl3, close) and close < Tsl3, title='Supertrend 3 Short', message='Supertrend 3: Short')
alertcondition(Trend3 == 1 and Trend3[1] == -1, title='Supertrend 3 Trend Change to Long', message='Supertrend 3: Trend Changed to Long')
alertcondition(Trend3 == -1 and Trend3[1] == 1, title='Supertrend 3 Trend Change to Short', message='Supertrend 3: Trend Changed to Short')