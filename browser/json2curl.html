<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>JSON to cURL Converter</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
        background-color: #f4f4f9;
        color: #333;
      }
      h1 {
        color: #444;
      }
      textarea {
        width: 100%;
        height: 300px;
        padding: 10px;
        font-family: monospace;
        font-size: 14px;
        border: 1px solid #ccc;
        border-radius: 5px;
        background-color: #fff;
        resize: vertical;
      }
      button {
        margin-top: 20px;
        padding: 10px 20px;
        font-size: 16px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
      }
      button:hover {
        background-color: #0056b3;
      }
      pre {
        margin-top: 20px;
        padding: 10px;
        background-color: #282c34;
        color: #abb2bf;
        border-radius: 5px;
        overflow-x: auto;
      }
      .response {
        margin-top: 20px;
        padding: 10px;
        background-color: #e9ecef;
        border: 1px solid #ccc;
        border-radius: 5px;
      }
      .copy-button {
        margin-top: 10px;
        background-color: #28a745;
      }
      .copy-button:hover {
        background-color: #218838;
      }
      .output-container {
        margin-top: 20px;
      }
      .output-container h2 {
        margin-bottom: 10px;
      }
    </style>
  </head>
  <body>
    <h1>JSON to cURL Converter</h1>
    <textarea id="jsonInput" placeholder="Paste your JSON here..."></textarea>
    <button onclick="sendRequest()">Send Request</button>

    <div class="output-container">
      <h2>Generated cURL Command (with \):</h2>
      <pre id="curlCommandWithSlash"></pre>
      <button
        class="copy-button"
        onclick="copyCurlCommand('curlCommandWithSlash')"
      >
        Copy cURL Command
      </button>
    </div>

    <div class="output-container">
      <h2>Generated cURL Command (without \):</h2>
      <pre id="curlCommandWithoutSlash"></pre>
      <button
        class="copy-button"
        onclick="copyCurlCommand('curlCommandWithoutSlash')"
      >
        Copy cURL Command
      </button>
    </div>

    <h2>API Response:</h2>
    <div class="response" id="apiResponse"></div>

    <script>
      function sendRequest() {
        const jsonInput = document.getElementById("jsonInput").value;
        console.log("Raw Input:", jsonInput); // Debugging: Log the raw input

        let jsonData;
        try {
          jsonData = JSON.parse(jsonInput);
        } catch (e) {
          alert("Invalid JSON! Please check your input.");
          console.error("JSON Parsing Error:", e); // Debugging: Log the error
          return;
        }

        const { method, url, data = {}, params = {}, headers } = jsonData;

        // Build the URL with query parameters for GET requests
        let finalUrl = url;
        if (method.toUpperCase() === "GET" && Object.keys(params).length > 0) {
          const queryParams = new URLSearchParams(params).toString();
          finalUrl = `${url}?${queryParams}`;
        }

        // Generate the cURL command with \ and newlines
        const curlHeadersWithSlash = Object.entries(headers)
          .map(([key, value]) => `--header '${key}: ${value}'`)
          .join(" \\\n  "); // Add line breaks and indentation for headers

        let curlDataWithSlash = "";
        if (method.toUpperCase() !== "GET" && Object.keys(data).length > 0) {
          curlDataWithSlash = JSON.stringify(data, null, 2) // Pretty-print the JSON data
            .split("\n") // Split into lines
            .map((line, index) => (index === 0 ? line : `  ${line}`)) // Indent all lines except the first
            .join(" \\\n  "); // Add line breaks and indentation
        }

        const curlCommandWithSlash = `curl --location '${finalUrl}' \\\n  --request ${method} \\\n  ${curlHeadersWithSlash}${
          curlDataWithSlash ? ` \\\n  --data '${curlDataWithSlash}'` : ""
        }`;

        // Generate the cURL command without \ but with newlines
        const curlHeadersWithoutSlash = Object.entries(headers)
          .map(([key, value]) => `--header '${key}: ${value}'`)
          .join("\n  "); // Add line breaks and indentation for headers

        let curlDataWithoutSlash = "";
        if (method.toUpperCase() !== "GET" && Object.keys(data).length > 0) {
          curlDataWithoutSlash = JSON.stringify(data, null, 2) // Pretty-print the JSON data
            .split("\n") // Split into lines
            .map((line, index) => (index === 0 ? line : `  ${line}`)) // Indent all lines except the first
            .join("\n  "); // Add line breaks and indentation
        }

        const curlCommandWithoutSlash = `curl --location '${finalUrl}'\n  --request ${method}\n  ${curlHeadersWithoutSlash}${
          curlDataWithoutSlash ? `\n  --data '${curlDataWithoutSlash}'` : ""
        }`;

        // Display the formatted cURL commands
        document.getElementById("curlCommandWithSlash").textContent =
          curlCommandWithSlash;
        document.getElementById("curlCommandWithoutSlash").textContent =
          curlCommandWithoutSlash;

        // Send the request using fetch
        fetch(finalUrl, {
          method: method,
          headers: headers,
          body: method.toUpperCase() !== "GET" ? JSON.stringify(data) : null,
        })
          .then((response) => response.json())
          .then((data) => {
            document.getElementById("apiResponse").textContent = JSON.stringify(
              data,
              null,
              2
            );
          })
          .catch((error) => {
            document.getElementById(
              "apiResponse"
            ).textContent = `Error: ${error.message}`;
          });
      }

      function copyCurlCommand(elementId) {
        const curlCommand = document.getElementById(elementId).textContent;
        navigator.clipboard
          .writeText(curlCommand)
          .then(() => {
            alert("cURL command copied to clipboard!");
          })
          .catch((error) => {
            alert("Failed to copy cURL command: " + error.message);
          });
      }
    </script>
  </body>
</html>
