// TradingView Watchlist Stock Adder
// This script should be run in the browser console while on TradingView's website
// Make sure you're logged in and have the watchlist open

class TradingViewWatchlistAdder {
  constructor(config = {}) {
      this.delay = config.delay || 1000; // Delay between adding stocks (ms)
      this.retryDelay = config.retryDelay || 2000; // Delay before retry on failure
      this.maxRetries = config.maxRetries || 3; // Maximum number of retries per stock
  }

  /**
   * Sleep function to create delays
   * @param {number} ms - Milliseconds to sleep
   */
  sleep(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Clicks the add symbol button
   * @returns {Promise<boolean>} - Success status
   */
  async clickAddSymbolButton() {
      const addButton = document.querySelector('[data-name="add-symbol-button"]');
      if (!addButton) {
          console.error('Add symbol button not found');
          return false;
      }
      addButton.click();
      await this.sleep(500);
      return true;
  }

  /**
   * Types the symbol name into the search input
   * @param {string} symbol - Stock symbol to type
   * @returns {Promise<boolean>} - Success status
   */
  async typeSymbol(symbol) {
      const input = document.querySelector('input[data-role="search"]');
      if (!input) {
          console.error('Search input not found');
          return false;
      }

      // Clear existing text
      input.value = '';
      input.dispatchEvent(new Event('input', { bubbles: true }));

      // Type new symbol
      input.value = symbol;
      input.dispatchEvent(new Event('input', { bubbles: true }));

      await this.sleep(500);
      return true;
  }

  /**
   * Selects the first search result
   * @returns {Promise<boolean>} - Success status
   */
  async selectFirstResult() {
      const firstResult = document.querySelector('[data-role="list-item"]');
      if (!firstResult) {
          console.error('No search results found');
          return false;
      }
      firstResult.click();
      return true;
  }

  /**
   * Adds a single stock to the watchlist
   * @param {string} symbol - Stock symbol to add
   * @returns {Promise<boolean>} - Success status
   */
  async addStock(symbol) {
      let retries = 0;

      while (retries < this.maxRetries) {
          try {
              // Click add button
              if (!await this.clickAddSymbolButton()) {
                  throw new Error('Failed to click add button');
              }

              // Type symbol
              if (!await this.typeSymbol(symbol)) {
                  throw new Error('Failed to type symbol');
              }

              // Select first result
              if (!await this.selectFirstResult()) {
                  throw new Error('Failed to select result');
              }

              console.log(`✅ Successfully added ${symbol}`);
              return true;

          } catch (error) {
              console.error(`Failed to add ${symbol}: ${error.message}`);
              retries++;

              if (retries < this.maxRetries) {
                  console.log(`Retrying ${symbol} (attempt ${retries + 1}/${this.maxRetries})...`);
                  await this.sleep(this.retryDelay);
              }
          }
      }

      console.error(`❌ Failed to add ${symbol} after ${this.maxRetries} attempts`);
      return false;
  }

  /**
   * Adds multiple stocks to the watchlist
   * @param {string[]} symbols - Array of stock symbols to add
   * @returns {Promise<void>}
   */
  async addStocks(symbols) {
      console.log(`Starting to add ${symbols.length} symbols to watchlist...`);

      const results = {
          success: [],
          failed: []
      };

      for (const symbol of symbols) {
          const success = await this.addStock(symbol);
          if (success) {
              results.success.push(symbol);
          } else {
              results.failed.push(symbol);
          }
          await this.sleep(this.delay);
      }

      // Print summary
      console.log('\nSummary:');
      console.log(`✅ Successfully added: ${results.success.length} symbols`);
      console.log(`❌ Failed to add: ${results.failed.length} symbols`);

      if (results.failed.length > 0) {
          console.log('\nFailed symbols:', results.failed);
      }
  }
}

// Example usage:
const watchlistAdder = new TradingViewWatchlistAdder({
  delay: 1000,      // Wait 1 second between stocks
  retryDelay: 2000, // Wait 2 seconds before retrying failed attempts
  maxRetries: 3     // Try up to 3 times per stock
});

// Add multiple stocks
const symbols = ['AMZN','VKTX','CVLT','FMC','HURN','META','MSFT','RDN','TECH','AAPL'];
watchlistAdder.addStocks(symbols).then(() => {
  console.log('Finished adding stocks');
});

