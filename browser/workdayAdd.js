// yohconnex.com
function wait4Elm(selector, elementNumber) {
  return new Promise((resolve) => {
    if (document.querySelectorAll(selector)[elementNumber]) {
      return resolve(document.querySelectorAll(selector)[elementNumber]);
    }

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (!mutation.addedNodes) return;

        for (let i = 0; i < mutation.addedNodes.length; i++) {
          // do things to your newly added nodes here
          let node = mutation.addedNodes[i];
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: false,
      characterData: false,
    });

    // stop watching using:
    observer.disconnect();
  });
}

wait4Elm("[data-uxi-widget-type='selectinput']", 0).then((elem1) => {
  console.log('elem1', elem1);
  elem1.click();
  wait4Elm("[data-uxi-widget-type='popup']", 0).then((elem2) => {
    console.log('elem2', elem2);
    elem2.click();
    wait4Elm("[data-automation-id='promptLeafNode']", 0).then((elem3) => {
      console.log('elem3', elem3);
      elem3.click();
      wait4Elm("[data-automation-id='promptLeafNode']", 0).then((elem4) => {
        console.log('elem4', elem4);
        elem4.click();
      });
    });
  });
});

wait4Elm("p-dropdown .ui-dropdown-label-container", 2).then((elem1) => {
  elem1.click();
  wait4Elm("p-dropdownitem .ng-star-inserted", 8).then((elm2) => {
    elm2.click();
    wait4Elm("p-dropdown .ui-dropdown-label-container", 3).then((elm3) => {
      elm3.click();
      wait4Elm("p-dropdownitem .ng-star-inserted", 0).then((elm4) => {
        elm4.click();
        wait4Elm("p-dropdown .ui-dropdown-label-container", 4).then((elm5) => {
          elm5.click();
          wait4Elm("p-dropdownitem .ng-star-inserted", 0).then((elm6) => {
            elm6.click();
            wait4Elm("p-dropdown .ui-dropdown-label-container", 5).then(
              (elem7) => {
                elem7.click();
                wait4Elm("p-dropdownitem .ng-star-inserted", 4).then((elm8) => {
                  elm8.click();
                  wait4Elm("p-dropdown .ui-dropdown-label-container", 6).then(
                    (elm8) => {
                      elm8.click();
                      wait4Elm("p-dropdownitem .ng-star-inserted", 0).then(
                        (elm9) => {
                          elm9.click();
                          wait4Elm(
                            "p-dropdown .ui-dropdown-label-container",
                            7
                          ).then((elm10) => {
                            elm10.click();
                            wait4Elm(
                              "p-dropdownitem .ng-star-inserted",
                              1
                            ).then((elm11) => {
                              elm11.click();
                              $$("button.ui-button-text-only")[1].click();
                            });
                          });
                        }
                      );
                    }
                  );
                });
              }
            );
          });
        });
      });
    });
  });
});