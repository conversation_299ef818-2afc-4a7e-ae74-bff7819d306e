// Prompt: give me javascript code to extract table data from crypto trading table where each row has class 'el-table__row' and columns are Type(having class 'order-type' and 'fit-tc-primary' for symbol), Quantity(with class 'fit-tc-primary'), Open Price(with class 'fit-tc-tip'), Close Price(with class 'fit-tc-tip'), PnL(with class 'fit-tc-rise' or 'fit-tc-fall'), Fee(with class 'fit-tc-tip'), and Time(with class 'fit-tc-tip'). Each cell has attribute 'data-v-ba5febfa'. Save it as CSV file with name format 'bfxcoin-transaction-records-<timestamp>.csv'. Also calculate and show total PnL, total fees, and final profit/loss per trading pair in console
// Tags: javascript, web-scraping, data-extraction, csv-export, trading-data, cryptocurrency, table-parsing, dom-manipulation, profit-loss-calculation, html-table, data-analysis, financial-calculations, browser-console, download-file, timestamp-formatting, data-processing, element-selector, class-selector, attribute-selector, string-manipulation

function extractTableData() {
  // Get all rows futures-orders-content table-border-header-null
  const rows = Array.from(document.querySelectorAll('.futures-orders-content tbody tr.el-table__row'));
  console.log(`Found ${rows.length} rows`);

  // Initialize CSV data with headers
  let csvContent = '"Type","Symbol","Quantity","Open Price","Close Price","PnL","Fee","Time"\n';

  // Initialize totals
  let totalPnL = 0;
  let totalFees = 0;
  let symbolStats = {};

  // Process each row
  rows.forEach((row, index) => {
      try {
          // Type and Symbol from first cell (using deeper nesting structure)
          const cells = row.querySelectorAll('.el-table__cell');
          const firstCell = cells[0];
          const type = firstCell.querySelector('.order-type').textContent.trim();
          const symbol = firstCell.querySelector('.fit-tc-primary').textContent.trim();

          // Quantity from second cell (using deeper nesting)
          const quantity = cells[1].textContent.replace('PCS', '').trim();

          // Prices (using complete path)
          const openPrice = cells[2].textContent.trim();
          const closePrice = cells[3].textContent.trim();

          // PnL (checking both rise and fall classes)
          const pnlElement = cells[4];
          const pnl = parseFloat(pnlElement.textContent.replace(',', '').trim());

          // Fee
          const fee = parseFloat(cells[5].textContent.replace(',', '').trim());

          // Time
          const time = cells[6].textContent.trim();

          // Update totals
          totalPnL += pnl;
          totalFees += fee;

          // Update symbol stats
          if (!symbolStats[symbol]) {
              symbolStats[symbol] = {
                  pnl: 0,
                  fees: 0,
                  trades: 0
              };
          }
          symbolStats[symbol].pnl += pnl;
          symbolStats[symbol].fees += fee;
          symbolStats[symbol].trades += 1;

          // Create CSV row with proper escaping
          const escapedRow = [type, symbol, quantity, openPrice, closePrice, pnl, fee, time]
              .map(field => `"${field.toString().replace(/"/g, '""')}"`)
              .join(',');

          csvContent += escapedRow + '\n';

          console.log(`Row ${index + 1} processed successfully`);

      } catch (error) {
          console.error(`Error processing row ${index + 1}:`, error);
          // Log the HTML of the problematic row for debugging
          console.log('Problematic row HTML:', row.innerHTML);
      }
  });

  // Generate timestamp
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
  const filename = `bfxcoin-transaction-records-${timestamp}.csv`;

  // Create and trigger download
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  // Calculate final results
  const finalProfit = totalPnL - Math.abs(totalFees);

  // Print summary
  console.log('\n=== Trading Summary ===');
  console.log(`Total Trades: ${rows.length}`);
  console.log(`Total PnL: ${totalPnL.toFixed(2)} USDT`);
  console.log(`Total Fees: ${Math.abs(totalFees).toFixed(2)} USDT`);
  console.log(`Final Profit/Loss: ${finalProfit.toFixed(2)} USDT`);

  // Print per-symbol breakdown
  console.log('\n=== Per Symbol Breakdown ===');
  Object.entries(symbolStats).forEach(([symbol, stats]) => {
      const netProfit = stats.pnl - Math.abs(stats.fees);
      console.log(`\n${symbol}:`);
      console.log(`  Trades: ${stats.trades}`);
      console.log(`  PnL: ${stats.pnl.toFixed(2)} USDT`);
      console.log(`  Fees: ${Math.abs(stats.fees).toFixed(2)} USDT`);
      console.log(`  Net Profit/Loss: ${netProfit.toFixed(2)} USDT`);
  });

  console.log(`\nData saved to ${filename}`);
}

extractTableData();