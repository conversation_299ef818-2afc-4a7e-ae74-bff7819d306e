// n8n Code Node - Using this.helpers.httpRequest
// This is the preferred way to make HTTP requests in n8n

const tickerMapping = {
  BTCUSD: "BTC/USD",
};

// Main function
async function execute() {
  // Set your Alpaca API credentials
  const ALPACA_API_KEY = "PKCHTDPY6ZOM149I1CR0";
  const ALPACA_SECRET_KEY = "YdMupOviCyZZa5IbvgZ2iUwJh6pks6ouHdOsAkKh";
  const BASE_URL = "https://paper-api.alpaca.markets"; // Use paper trading URL

  try {
    // Example 1: Get account information
    const accountResponse = await this.helpers.httpRequest({
      method: "GET",
      url: `${BASE_URL}/v2/account`,
      headers: {
        "APCA-API-KEY-ID": ALPACA_API_KEY,
        "APCA-API-SECRET-KEY": ALPACA_SECRET_KEY,
        Accept: "application/json",
      },
    });

    // Example 2: Get open orders
    const ordersResponse = await this.helpers.httpRequest({
      method: "GET",
      url: `${BASE_URL}/v2/orders`,
      qs: {
        // Query parameters
        status: "open",
        limit: 100,
      },
      headers: {
        "APCA-API-KEY-ID": ALPACA_API_KEY,
        "APCA-API-SECRET-KEY": ALPACA_SECRET_KEY,
        Accept: "application/json",
      },
    });
    console.log("👉🏼 ~ open orders:", ordersResponse);

    // Get positions to check available quantities
    const positionsResponse = await this.helpers.httpRequest({
      method: "GET",
      url: `${BASE_URL}/v2/positions`,
      headers: {
        "APCA-API-KEY-ID": ALPACA_API_KEY,
        "APCA-API-SECRET-KEY": ALPACA_SECRET_KEY,
        Accept: "application/json",
      },
    });
    console.log("👉🏼 ~ positions:", positionsResponse);

    const request = JSON.parse(JSON.stringify($json));
    const body = JSON.parse(JSON.stringify(request.body));
    console.log("👉🏼 ~ execute ~ body:", body);

    const orderSide = body?.strategy?.order?.action;
    const ticker = tickerMapping[body?.ticker];
    let orderQuantity = "0.0001"; // Default quantity

    // Check if there's already a position for this ticker when placing a sell order
    if (orderSide === "sell") {
      console.log("👉🏼 ~ execute ~ orderSide:", orderSide, ticker);
      const existingPosition = positionsResponse.find(
        (position) => position.symbol === body?.ticker
      );
      console.log("👉🏼 ~ execute ~ existingPosition:", existingPosition);

      if (!existingPosition || parseFloat(existingPosition.qty) <= 0) {
        console.log(
          `⚠️ No existing position found for ${ticker} or quantity is 0. Sell order will be skipped.`
        );
        return {
          account: accountResponse,
          openOrders: ordersResponse,
          positions: positionsResponse,
          newOrder: null,
          message: `Sell order skipped: No existing position found for ${ticker}`,
          existingPosition: existingPosition || null,
        };
      } else {
        // Use the available quantity from the position
        orderQuantity = Math.abs(parseFloat(existingPosition.qty)).toString();
        console.log(
          `✅ Existing position found for ${ticker}. Available quantity: ${orderQuantity}`,
          existingPosition
        );
      }
    }

    // Example 3: Place a market order
    const postOptions = {
      method: "POST",
      url: `${BASE_URL}/v2/orders`,
      headers: {
        "APCA-API-KEY-ID": ALPACA_API_KEY,
        "APCA-API-SECRET-KEY": ALPACA_SECRET_KEY,
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      body: {
        symbol: ticker,
        qty: orderQuantity,
        side: orderSide,
        type: "market",
        time_in_force: "gtc",
      },
      json: true, // Automatically stringify the body
    };
    console.log("👉🏼 ~ execute ~ postOptions:", postOptions);
    const placeOrderResponse = await this.helpers.httpRequest(postOptions);

    // Return the data to be used in subsequent nodes
    return {
      account: accountResponse,
      openOrders: ordersResponse,
      positions: positionsResponse,
      newOrder: placeOrderResponse,
      orderQuantity: orderQuantity,
    };
  } catch (error) {
    // Error handling
    if (error.response) {
      console.error("Response error:", error.response.body);
      throw new Error(
        `API error: ${error.response.statusCode} - ${JSON.stringify(
          error.response.body
        )}`
      );
    } else {
      console.error("Error:", error.message);
      throw new Error(`Request failed: ${error.message}`);
    }
  }
}

// Return the result
return await execute.call(this); // Important: maintain the context for this.helpers
