import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from tqdm import tqdm
import os

def get_stock_list():
    stock_list = []

    try:
        with open('data/stock_tickers.txt', 'r') as file:
            stock_list = [line.strip().rstrip(',') for line in file if line.strip()]
    except FileNotFoundError:
        print("data/stock_tickers.txt not found.")

    return stock_list

def get_output_filename():
    """Generate a unique filename based on the current script name and timestamp."""
    script_name = os.path.splitext(os.path.basename(__file__))[0]
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return [f"{script_name}_{timestamp}.txt", f"{script_name}_details_{timestamp}.txt"]

def fetch_data(symbol, start_date, end_date, fourHour=False):
    if fourHour == True:
        # Fetch 1-hour data and resample to 4-hour timeframe
        data = yf.download(symbol, start=start_date, end=end_date, interval='1h', progress=False)
        data_4h = data.resample('4h').agg({
            'Open': 'first',
            'High': 'max',
            'Low': 'min',
            'Close': 'last',
            'Volume': 'sum'
        })
        return data_4h.dropna()
    else:
        return yf.download(symbol, start=start_date, end=end_date, progress=False)

def bollinger_bands(df, length=20, mult=2.0, ma_type='SMA', src='Close'):
    if ma_type == 'SMA':
        basis = df[src].rolling(window=length).mean()
    elif ma_type == 'EMA':
        basis = df[src].ewm(span=length, adjust=False).mean()
    elif ma_type == 'SMMA':
        basis = df[src].ewm(alpha=1/length, adjust=False).mean()
    elif ma_type == 'WMA':
        weights = np.arange(1, length + 1)
        basis = df[src].rolling(window=length).apply(lambda x: np.dot(x, weights) / weights.sum(), raw=True)
    elif ma_type == 'VWMA':
        basis = (df[src] * df['Volume']).rolling(window=length).sum() / df['Volume'].rolling(window=length).sum()

    dev = mult * df[src].rolling(window=length).std()
    upper = basis + dev
    lower = basis - dev

    return pd.DataFrame({'Basis': basis, 'Upper': upper, 'Lower': lower})

def scan_bollinger_breakouts(symbols, length=20, mult=2.0, ma_type='SMA', lookback=7):
    breakouts = []

    for symbol in tqdm(symbols, desc="Scanning stocks"):
        try:
            df = yf.download(symbol.replace('.', '-'), period="1mo", progress=False)
            if len(df) < length:
                continue

            bb = bollinger_bands(df, length, mult, ma_type)
            df = df.join(bb)

            # Check for breakout in the last candle
            last_close = df['Close'].iloc[-1]
            last_upper = df['Upper'].iloc[-1]
            last_lower = df['Lower'].iloc[-1]

            # Check for high volume
            avg_volume = df['Volume'].iloc[-lookback:].mean()
            last_volume = df['Volume'].iloc[-1]

            if (last_close > last_upper and last_volume > avg_volume * 1.5) or \
               (last_close < last_lower and last_volume > avg_volume * 1.5):
                breakouts.append({
                    'Symbol': symbol,
                    'Close': last_close,
                    'Upper BB': last_upper,
                    'Lower BB': last_lower,
                    'Volume': last_volume,
                    'Avg Volume': avg_volume
                })

        except Exception as e:
            print(f"Error processing {symbol}: {str(e)}")

    return pd.DataFrame(breakouts)

def main():
    symbols = get_stock_list()
    results = []

    print("Scanning for Bollinger Band breakouts...")
    results = scan_bollinger_breakouts(symbols)

    print(results)
    if len(results):
        print("\nStocks with Bollinger Band breakouts in the last week:")
        for index, row in results.iterrows():
            print(f"{row['Symbol']}")

        # Save results to a dynamically named file
        output_filenames = get_output_filename()
        with open(output_filenames[0], 'w') as f:
            for index, row in results.iterrows():
                f.write(f"{row['Symbol']}\n")
    else:
        print("\nNo Bollinger Band breakouts found in the last week.")

if __name__ == "__main__":
    main()
