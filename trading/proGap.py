import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta
import asyncio
import aiohttp
from tqdm import tqdm
import logging
from io import StringIO
import os

logging.basicConfig(level=logging.INFO)

def get_output_filename():
    """Generate a unique filename based on the current script name and timestamp."""
    script_name = os.path.splitext(os.path.basename(__file__))[0]
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return [f"{script_name}_{timestamp}.txt", f"{script_name}_details_{timestamp}.txt"]

async def get_sp500_symbols():
    url = "https://en.wikipedia.org/wiki/List_of_S%26P_500_companies"
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            html = await response.text()
    df = pd.read_html(StringIO(html))[0]
    return df['Symbol'].tolist()

def is_pro_gap(current_candle, previous_candle):
    # Bullish Pro Gap
    if (previous_candle['Close'] < previous_candle['Open'] and  # Previous candle is red
        current_candle['Open'] > previous_candle['High'] and    # Current candle opens above previous high
        current_candle['Close'] > current_candle['Open']):      # Current candle is green
        return 'Bullish'

    # Bearish Pro Gap
    elif (previous_candle['Close'] > previous_candle['Open'] and  # Previous candle is green
          current_candle['Open'] < previous_candle['Low'] and     # Current candle opens below previous low
          current_candle['Close'] < current_candle['Open']):      # Current candle is red
        return 'Bearish'

    return None

async def find_pro_gaps(symbol, start_date, end_date):
    try:
        ticker = yf.Ticker(symbol.replace('.', '-'))
        data = ticker.history(start=start_date, end=end_date)
        pro_gaps = []
        for i in range(1, len(data)):
            gap_type = is_pro_gap(data.iloc[i], data.iloc[i-1])
            if gap_type:
                pro_gaps.append((len(data) - i, data.index[i].date(), gap_type))
        return symbol, pro_gaps
    except Exception as e:
        logging.error(f"Error processing {symbol}: {str(e)}")
        return symbol, []

async def main():
    end_date = datetime.now()
    start_date = end_date - timedelta(days=14)

    symbols = await get_sp500_symbols()
    symbols.extend(['QQQ', 'SPY'])

    tasks = [find_pro_gaps(symbol, start_date, end_date) for symbol in symbols]

    results = []
    for task in tqdm(asyncio.as_completed(tasks), total=len(tasks), desc="Processing symbols"):
        results.append(await task)

    # Save results to a dynamically named file
    output_filenames = get_output_filename()

    with open(output_filenames[0], 'w') as f:
        for symbol, pro_gaps in sorted(results):
            if pro_gaps:
                f.write(f"{symbol}\n")

    with open(output_filenames[1], 'w') as f:
        for symbol, pro_gaps in sorted(results):
            if pro_gaps:
                f.write(f"{symbol}:\n")
                for position, date, gap_type in pro_gaps:
                    f.write(f"  Position: {position}, Date: {date}, Type: {gap_type}\n")
                f.write("\n")

    print("Results:")
    for symbol, pro_gaps in sorted(results):
        if pro_gaps:
            print(f"{symbol}:")
            for position, date, gap_type in pro_gaps:
                print(f"  Position: {position}, Date: {date}, Type: {gap_type}")
            print()

if __name__ == "__main__":
    asyncio.run(main())