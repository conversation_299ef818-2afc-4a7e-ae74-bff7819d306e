<!-- Scripts -->
<!-- TradingView Screener Watchlist -
(function(console){

    console.save = function(data, filename){

        if(!data) {
            console.error('Console.save: No data')
            return;
        }

        if(!filename) filename = 'console.json'

        if(typeof data === "object"){
            data = JSON.stringify(data, undefined, 4)
        }

        var blob = new Blob([data], {type: 'text/json'}),
            e    = document.createEvent('MouseEvents'),
            a    = document.createElement('a')

        a.download = filename
        a.href = window.URL.createObjectURL(blob)
        a.dataset.downloadurl =  ['text/json', a.download, a.href].join(':')
        e.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null)
        a.dispatchEvent(e)
    }
})(console);
const data = [];
document.querySelectorAll("#js-screener-container a").forEach(itm => data.push(itm.textContent));
console.save(data.join(',\n'), `${document.querySelector("#js-screener-container h1").textContent}-${new Date().toISOString().split(".")[0].replaceAll(":", "-")}.txt`);
-->

Top Option indicators?
EMA - Exponential Moving Average
R-VWAP - Rolling Volume-Weighted Average Price
Super trand
Poivt point

Best Option Directional Strategy to make money? 30 Points

Options Selling point of view:
If the market cuts the bottom then Will sell the Up call
If the market cuts the top then will sell bottom put

When market going up then put will be in -ve
When market going down then call will be in -ve

So which ever is seening going negative we will sell that


Rules: 5m time frame for bars
I. Stock selecton in live market
  1. Stock movement between 3% to 6%
  2. Drastical point - When the stock price goes to high come back down then go back and touches the high again that point is a drastical point
II. How to Play?
  1. 3%-6% ralling volume of stock
  2. 2-by-2 rule - 2 green bar and 2 red bar
  3. last red candle bar volume must be less then first 2 red bars and first green bar volume in other words 4th bar volume must have lowest volume then first 3 bars
  4. Enter the trade as soon as low breaks for last red candle and SL will be highest price 2nd green bar and 3rd red candle bar price
  5. VWAP - Volume Weighted Average Price for exit or predicting reversal

[50 हज़ार लगाके 10 लाख Profit किया | Live Proof | Option Trading Strategy |SAGAR SINHA](https://www.youtube.com/watch?v=QCXfNTTmadY)
I. Stock selecton:
1. 9:20 Top gaind loosen
2. 2%
3. Open Intersest >= 7%
4. wait for 10 min candle for Index
  a. if green then play in top gainer and top looser
  b. if red then play only in top looser

II. Trade
1. Entry: 9:25 - Mark 9:25am High
  a. Top gainer - Buy Side - if it wont break until 10:30am dont take trade
    i. 2 candle up - Set the Top of 2nd candle high at 9:25am       / - Resistance
    ii. when the 9:25 high breaks then enter when it Breaks up - /\/
  b. Top looser - Sell Side - if it wont break until 10:30am dont take trade
    i. 2 candle down - Set the Bottom of 2nd candle low at 9:25am
    ii. when the 9:25 low breaks then enter when it Breaks down - \/\ - Support
                                                                     \
2. Exit:
  a. 2 candle close below 8 SMA (Simple Moving Average) or
  b. 1 candle close above 3 SMA (Simple Moving Average)


# #100daychallenge

Controlling Emotions: https://youtu.be/PdP-uQtC30E?si=e22rEwgZWhzXTN_v
1. Every 15 days - Full rest of Share market - No watching No Streaming
2. Once a month Just streaming No trading

Drastical point: https://www.youtube.com/watch?v=yptX9WqvPSI - 100 day challenge
1. Find Supply - When stock falls atleast -10% from top - that area is supply
  a. last green candle make high and low
2. Max 3 attempt on Sell side: 1st attempt sell on supply then try on 2nd time not more then 3 times
3. Find Demand - When stock rise atleast 10% from bottom - that area is demand
4. Max 2 attempt on Buy side: 1st attempt buy on demand then try on 2nd time not more then 2 times

Rules:
1. Loss: Never ever try to AVERAGE in loss conditions
2. Which scenarios I play better
3. Note logic behind trades - tukka trading / market operator
4. Fundas: After trade only watch chart [](https://youtu.be/PdP-uQtC30E?si=vzotbZ7QY1TOu3hD&t=1185)
  a. After trade SL and Exit: [TIME base SL not chart base SL]If 5 min trade - goto 15 mins / 30 mins and check if that low is hitting then exit the trade
    a.example - if timeframe 5m / 10min / 15min, check last 30min low and last 2/3 days which point market come down below / move low
    b.example - 5min timeframe and trade enterted. 30 min low is my SL and keep increasing it every time
    c.example -