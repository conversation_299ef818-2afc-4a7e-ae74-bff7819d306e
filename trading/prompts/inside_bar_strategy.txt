# # https://www.youtube.com/watch?v=g6RZ4U44qMw&t=4840s
# Stock selction
0. Inside bar and following
1. Volume
2. RSI
3. MA - Super trail

# Patterns
1. Rounding Bottom Pattern strategy on screen
  a.
2. Flag Chart Pattern strategy live trading examples
  a.

### Prompt
"
Create a Python script named 'sp5_inside_bar_screener.py' that screens S&P 500 stocks for inside bar patterns. The script should:

Retrieve the current S&P 500 ticker list from Wikipedia.
For each stock, fetch the last 30 days of data using yfinance.
Implement an inside bar screening function with the following conditions:
a. Current day's high is less than the previous day's high
b. Current day's low is greater than the previous day's low
c. Current day's volume is greater than 100,000
d. Current day's close is greater than 100
e. Any one of the next few candles (up to 3 by default) closes above the current candle's high or below the current candle's low
Iterate through the data from the most recent day backwards.
Return the position of the pattern relative to the most recent data point.
Implement error handling for each stock.
Show progress using a tqdm progress bar.
Output a list of tickers that meet all criteria, including their positions.
Show a final count of stocks passing the screen.
Save the results (ticker symbols only) to a file named 'insider-bar-hits-YYYY-MM-DDTHH-MM-SS.txt'.

Use pandas for data manipulation, requests and BeautifulSoup for fetching S&P 500 tickers, and yfinance for fetching stock data. Ensure the script handles ticker symbols that may contain periods by replacing them with hyphens when fetching data from yfinance.
Format the code with proper indentation and include brief comments explaining key sections. The script should be complete and runnable, assuming the required libraries are installed."
This prompt covers all the key aspects of the script and should result in code very similar to, if not exactly the same as, the one we've discussed. The specific implementation details might vary slightly depending on the AI's approach, but the core functionality and structure should be consistent with the provided code.
"

```
### System Prompt:
You are an expert Python programmer with extensive knowledge of financial markets, particularly options trading. You specialize in creating scripts that interact with financial APIs and process market data. Your code is clean, well-commented, and follows best practices for error handling and performance optimization.

### User Prompt:
Please write a Python script that fetches data for the top 100 most actively traded options contracts amongs all the S&P 500 company stocks. The script should do the following:

1. Use the `yfinance` library to fetch options data, as it's more reliable for accessing Yahoo Finance data.
2. Include a function to get a list of popular stock tickers (at least 10-15 tickers).
3. For each ticker, fetch the options chain for the nearest expiration date.
4. Combine all the options data, including both calls and puts.
5. Sort the combined data by trading volume and select the top 100 options.
6. Include proper error handling and logging throughout the script.
7. Implement a delay between API calls to avoid rate limiting issues.
8. Provide a function to save the results to a CSV file.
9. Use a main function to orchestrate the entire process.

Additional requirements:
- Use environment variables (with `python-dotenv`) for any sensitive information.
- Include clear comments explaining each major step of the process.
- Use pandas for data manipulation.
- Implement warning suppression for any known deprecation warnings.
- Provide print statements to show the progress of the script as it runs.

Please write the complete script, including all necessary imports and function definitions. The script should be ready to run with minimal setup required by the user.
```