<!-- https://chartink.com/screener/tig-pro-gap-candle-daily-buy-1 -->

S&P 500 Stock Screening Criteria
Follow these steps to screen for S&P 500 stocks based on technical analysis:

15-Minute Timeframe

Current 15-minute close is above the high from 1 day ago


Daily Timeframe - Price Action

Today's open is greater than yesterday's close
Today's open is greater than yesterday's close
Today's open is less than yesterday's open
Today's close is greater than today's open
Today's close is above yesterday's high


Daily Timeframe - Moving Averages

Today's close is greater than the 90-day Weighted Moving Average (WMA) of the daily high
Today's close is greater than the 21-day Exponential Moving Average (EMA) of the daily close


Weekly Timeframe

This week's close is greater than the 21-week EMA of the weekly close


Monthly Timeframe

This month's close is greater than the 21-month EMA of the monthly close


RSI (Relative Strength Index) Criteria

Weekly RSI(14) is greater than 60
Monthly RSI(14) is greater than 60
Quarterly RSI(14) is greater than 60



Implementation Notes:

Use a stock screening tool or platform that allows you to input these criteria for S&P 500 stocks.
Ensure your data feed provides up-to-date information for 15-minute, daily, weekly, monthly, and quarterly timeframes.
You may need to create custom indicators or use built-in functions for calculating WMA, EMA, and RSI values.
Adjust the timeframes if needed to match your specific trading or investment strategy.
Remember that these are technical criteria only. Consider fundamental analysis and overall market conditions as well.
Regularly review and adjust your screening criteria based on market conditions and your evolving strategy.

Caution:
Past performance and technical indicators do not guarantee future results. Always conduct thorough research and consider seeking professional financial advice before making investment decisions.

# PROMPT

# Prompt for S&P 500 Stock Screening Script

## System Prompt:
You are an expert Python programmer specializing in financial analysis and algorithmic trading. Your task is to create a Python script that screens S&P 500 stocks based on specific technical criteria. The script should use popular financial libraries like yfinance for data retrieval and pandas for data manipulation. Ensure the script is well-commented, efficient, and follows best practices for Python coding.

## User Prompt:
Please write a Python script that screens S&P 500 stocks based on the following criteria:

1. 15-Minute Timeframe:
   - Current 15-minute close is above the high from 1 day ago

2. Daily Timeframe - Price Action:
   - Today's open is greater than yesterday's close
   - Today's open is greater than yesterday's close
   - Today's open is less than yesterday's open
   - Today's close is greater than today's open
   - Today's close is above yesterday's high

3. Daily Timeframe - Moving Averages:
   - Today's close is greater than the 90-day Weighted Moving Average (WMA) of the daily high
   - Today's close is greater than the 21-day Exponential Moving Average (EMA) of the daily close

4. Weekly Timeframe:
   - This week's close is greater than the 21-week EMA of the weekly close

5. Monthly Timeframe:
   - This month's close is greater than the 21-month EMA of the monthly close

6. RSI (Relative Strength Index) Criteria:
   - Weekly RSI(14) is greater than 60
   - Monthly RSI(14) is greater than 60
   - Quarterly RSI(14) is greater than 60

Requirements for the script:

1. Use the yfinance library to fetch historical data for S&P 500 stocks.
2. Implement functions to calculate WMA, EMA, and RSI.
3. Create a main screening function that applies all criteria to each stock.
4. Handle potential errors and edge cases (e.g., missing data, API failures).
5. Optimize the script for performance, considering the large number of stocks and data points.
6. Print or save the list of stocks that pass all criteria.
7. Include comments explaining each major step of the process.

Additional considerations:
- The script should be able to run independently once initiated.
- Include a requirements.txt file or specify required libraries at the beginning of the script.
- Provide brief instructions on how to run the script and interpret the results.

Please write the complete Python script based on these requirements.

## Assistant Response:
[The AI should respond with a complete, well-structured Python script that meets all the specified requirements. The script should be ready to run with minimal modifications, include all necessary imports, function definitions, and a main execution block. The AI should also provide brief instructions on how to set up and run the script, including any required API keys or data sources.]