# SuperTrend Indicator Code Generation Prompt

Create a Python script that implements the SuperTrend indicator for stock analysis using yfinance for data retrieval and Plotly for visualization. The script should be modular, well-commented, and easy to understand. Follow these detailed steps and requirements:

## 1. Import Required Libraries
Import the following libraries at the beginning of the script:
```python
import yfinance as yf
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
```

## 2. Define SuperTrend Calculation Function
Create a function named `calculate_supertrend` with the following specifications:
- Parameters: `data` (DataFrame), `period` (int, default=22), `multiplier` (float, default=3)
- Calculate the Average True Range (ATR) using the specified period
- Compute upper and lower bands using the ATR and multiplier
- Implement the SuperTrend logic to determine trend direction
- Return a DataFrame with 'SuperTrend' and 'Direction' columns
- Use separate calculations for basic upper and lower bands, then refine them in a loop

## 3. Create Plotting Function
Develop a function named `plot_supertrend` that visualizes the data:
- Use Plotly's `make_subplots` to create a figure with two subplots (price and volume)
- Plot candlestick chart for price data
- Add SuperTrend line to the price chart, coloring it based on direction (green for uptrend, red for downtrend)
- Mark buy and sell signals with triangles (green for buy, red for sell)
- Include volume bars in the lower subplot
- Ensure proper labeling and layout configuration

## 4. Implement Main Function
Create a `main` function that ties everything together:
- Accept parameters for ticker symbol, date range, SuperTrend period, and multiplier
- Use yfinance to download historical data
- Calculate True Range for the SuperTrend function
- Call the SuperTrend calculation function
- Handle NaN values and align data
- Call the plotting function
- Display the last few rows of the SuperTrend data

## 5. Script Execution
Add a conditional to run the `main` function when the script is executed directly:
```python
if __name__ == "__main__":
    ticker = "SPY"  # Default to SPDR S&P 500 ETF Trust
    main(ticker)
```

## 6. Customization and Parameters
- Allow easy customization of SuperTrend parameters (period and multiplier)
- Set default date range to cover the last 365 days, ending on the current date
- Ensure the ability to pass custom date ranges to the `main` function

## 7. Error Handling and Validation
- Include basic error handling for data retrieval and processing
- Validate input parameters to ensure they are within reasonable ranges

## 8. Code Style and Documentation
- Follow PEP 8 guidelines for code formatting
- Include docstrings for each function explaining parameters, return values, and purpose
- Add inline comments for complex calculations or logic

## 9. Performance Considerations
- Optimize the SuperTrend calculation loop for efficiency
- Use vectorized operations where possible in pandas DataFrames

## 10. Visualization Enhancements
- Color the SuperTrend line segments based on direction (green for uptrend, red for downtrend)
- Implement buy and sell signals based on direction changes

By following this prompt, you should be able to consistently generate a well-structured and functional SuperTrend indicator script using Python, yfinance, and Plotly. The resulting code will be suitable for stock analysis and can be easily modified or extended for different use cases.