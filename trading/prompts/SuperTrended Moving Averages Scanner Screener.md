```
def calculate_supertrend(df, length=100, atr_period=10, multiplier=0.5, ma_type='EMA', change_atr=True):
    def sma(series, periods): return series.rolling(window=periods).mean()
    def ema(series, periods): return series.ewm(span=periods, adjust=False).mean()
    def wma(series, periods):
        weights = np.arange(1, periods + 1)
        return series.rolling(periods).apply(lambda x: np.dot(x, weights) / weights.sum(), raw=True)
    def dema(series, periods):
        ema1 = ema(series, periods)
        ema2 = ema(ema1, periods)
        return 2 * ema1 - ema2
    def tema(series, periods):
        ema1 = ema(series, periods)
        ema2 = ema(ema1, periods)
        return 3 * (ema1 - ema2) + ema(ema2, periods)
    def var_func(series, periods):
        alpha = 2 / (periods + 1)
        ud1 = np.where(series > series.shift(1), series - series.shift(1), 0)
        dd1 = np.where(series < series.shift(1), series.shift(1) - series, 0)
        ud = pd.Series(ud1).rolling(9).sum()
        dd = pd.Series(dd1).rolling(9).sum()
        cmo = (ud - dd) / (ud + dd)
        var = pd.Series(index=series.index)
        var = alpha * np.abs(cmo) * series + (1 - alpha * np.abs(cmo)) * var.shift(1)
        return var.fillna(method='bfill')
    def wwma(series, periods):
        alpha = 1 / periods
        wwma = pd.Series(index=series.index)
        wwma = alpha * series + (1 - alpha) * wwma.shift(1)
        return wwma.fillna(method='bfill')
    def zlema(series, periods):
        lag = (periods - 1) // 2
        ema_data = series + series - series.shift(lag)
        return ema(ema_data, periods)
    def tsf(series, periods):
        x = np.arange(periods)
        x_sum = x.sum()
        x2_sum = (x**2).sum()
        def tsf_calc(y):
            y_sum = y.sum()
            xy_sum = (x * y).sum()
            m = (periods * xy_sum - x_sum * y_sum) / (periods * x2_sum - x_sum**2)
            b = (y_sum - m * x_sum) / periods
            return b + m * periods
        return series.rolling(periods).apply(tsf_calc, raw=True)
    def hull(series, periods):
        return wma(2 * wma(series, periods // 2) - wma(series, periods), int(np.sqrt(periods)))
    def till(series, periods, t3_factor=0.7):
        e1 = ema(series, periods)
        e2 = ema(e1, periods)
        e3 = ema(e2, periods)
        e4 = ema(e3, periods)
        e5 = ema(e4, periods)
        e6 = ema(e5, periods)
        c1 = -t3_factor**3
        c2 = 3 * t3_factor**2 + 3 * t3_factor**3
        c3 = -6 * t3_factor**2 - 3 * t3_factor - 3 * t3_factor**3
        c4 = 1 + 3 * t3_factor + t3_factor**3 + 3 * t3_factor**2
        return c1 * e6 + c2 * e5 + c3 * e4 + c4 * e3

    ma_func = {'SMA': sma, 'EMA': ema, 'WMA': wma, 'DEMA': dema, 'TMA': tema,
               'VAR': var_func, 'WWMA': wwma, 'ZLEMA': zlema, 'TSF': tsf,
               'HULL': hull, 'TILL': till}.get(ma_type, ema)

    df['MA'] = ma_func(df['Close'], length)
    df['TR'] = np.maximum(df['High'] - df['Low'],
                          np.maximum(abs(df['High'] - df['Close'].shift(1)),
                                     abs(df['Low'] - df['Close'].shift(1))))
    df['ATR'] = df['TR'].ewm(span=atr_period, adjust=False).mean() if change_atr else df['TR'].rolling(window=atr_period).mean()

    df['Up'] = df['MA'] - multiplier * df['ATR']
    df['Dn'] = df['MA'] + multiplier * df['ATR']
    df['Trend'] = 0
    df['SuperTrend'] = np.nan

    for i in range(1, len(df)):
        curr, prev = df.index[i], df.index[i-1]
        if df.loc[curr, 'Close'] > df.loc[prev, 'Dn']:
            df.loc[curr, 'Trend'] = 1
        elif df.loc[curr, 'Close'] < df.loc[prev, 'Up']:
            df.loc[curr, 'Trend'] = -1
        else:
            df.loc[curr, 'Trend'] = df.loc[prev, 'Trend']

        if df.loc[curr, 'Trend'] == 1:
            df.loc[curr, 'Up'] = max(df.loc[curr, 'Up'], df.loc[prev, 'Up'])
            df.loc[curr, 'SuperTrend'] = df.loc[curr, 'Up']
        else:
            df.loc[curr, 'Dn'] = min(df.loc[curr, 'Dn'], df.loc[prev, 'Dn'])
            df.loc[curr, 'SuperTrend'] = df.loc[curr, 'Dn']

    return df
````
write a screener which scan all the stock symbols listed in S&P 500 list plus `QQQ` and `SPY` and find out which stock symbol has trend reversal in last one week and specify that symbol and date.
Give me full working script.