Write an artifact which scans s&p 500 and 'QQQ' and 'SPY' for Pro Gap
Also
1. for yf.Ticker(symbol) use yf.Ticker(symbol.replace('.', '-'))
2. fetch a year's worth of data at once and then resamples it for different timeframes
3. add more robust error handling, particularly in the data fetching process
4. provide more detailed logging, show progress using tqdm library
5. use better error handling for fetching the S&P 500 list and provides feedback on the process
6. seperate each functionality in its own distinct functions, which can be more modular.
7. resamples data for weekly and monthly (use 'ME' instead of 'M') calculations, which can be more accurate, especially for stocks that might not trade every day
8. fetch all data at once and then resample
9. include file output functionality, saving the results with a timestamp
10. handles edge cases better, such as potential issues with fetching the S&P 500 list or insufficient data for certain stocks
11. use end_date = datetime.now()
12. use start_date = end_date - timedelta(days=(365 * 5))
13. use library yfinance, pandas, pandas_ta, plotly

``````
Create a Python script for analyzing stock market data to identify all the "Pro Gaps" for S&P 500 companies plus QQQ and SPY ETFs over the last 14 days. The script should have the following features and structure:
Structure of Pro Gap candle is as below
Bullish Pro Gap:
a. The 1st candle (on the left) is red, indicating a down day.
b. The 2nd candle (on the right) is green and opens above the high of the 1st candle, creating a gap up. It also closes higher than it opens.

Bearish Pro Gap:
a. The 1st candle (on the left) is green, indicating an up day.
b. The 2nd candle (on the right) is red and opens below the low of the 1st candle, creating a gap down. It also closes lower than it opens.

Additional notes:
i. For a bullish pro gap, the first candle should be red in a strong bull trend.
ii. The second candle in a bullish pro gap should open above the close of the red candle and close above the red candle, maintaining a strong bullish trend.

1. Import these libraries: yfinance, pandas, datetime (datetime and timedelta), asyncio, aiohttp, tqdm, logging, and io (StringIO). For yf.Ticker(symbol) use yf.Ticker(symbol.replace('.', '-'))

2. Set up basic logging with logging.basicConfig(level=logging.INFO).

3. Create an asynchronous function called get_sp500_symbols() that:
   - Scrapes the Wikipedia page "List of S&P 500 companies" using aiohttp.
   - Uses pd.read_html() with StringIO to parse the HTML and extract symbols.
   - Returns a list of S&P 500 symbols.

4. Define a function is_pro_gap(current_candle, previous_candle) that:
   - Identifies Bullish Pro Gaps (current open > previous high).
   - Identifies Bearish Pro Gaps (current open < previous low).
   - Returns 'Bullish', 'Bearish', or None.

5. Create an asynchronous function find_pro_gaps(symbol, start_date, end_date) that:
   - Uses yfinance to fetch historical data for a given symbol.
   - Iterates through the data to find Pro Gaps.
   - Handles exceptions and logs errors.
   - Returns a tuple of (symbol, list of pro gaps).

6. Define an asynchronous main() function that:
   - Sets the date range (end_date as current date, start_date as 14 days prior).
   - Fetches S&P 500 symbols and adds 'QQQ' and 'SPY'.
   - Creates tasks for processing each symbol asynchronously.
   - Uses tqdm to display a progress bar while processing symbols.
   - Collects and prints results, showing the position reverse index of the candle, date and type of each Pro Gap for each symbol.

7. Use if __name__ == "__main__": to run asyncio.run(main()).

8. Save the alphabetically sorted symbol results (ticker symbols only) to a file named 'pro-gapped-YYYY-MM-DDTHH-MM-SS.txt'.

9. Save the alphabetically sorted symbol results to a file named 'pro-gapped-details-YYYY-MM-DDTHH-MM-SS.txt'.

Ensure the code uses asynchronous programming with asyncio and aiohttp for efficient data fetching and processing. Include error handling and logging. Use tqdm for progress reporting. The script should be structured to allow for easy modification and expansion in the future.
``````