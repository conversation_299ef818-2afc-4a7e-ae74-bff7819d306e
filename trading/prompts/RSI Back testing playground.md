RSI Back testing playground

Write an artifact which will scan all "S&P 500" and "QQQ" and "SPY" US Stocks and list all stocks which qualify following rules
1. Weekly RSI just bounce back from 60 after resistance or
2. Weekly RSI just bounce back from 40 after resistance
Also
1. Show progress bar
2. use end_date = datetime.now()
3. use start_date = end_date - timedelta(days=(365 * 5))
4. use library yfinance, pandas, pandas_ta
5. Output should show ticker symbol per line and no headers
6. save the same result in a file with naming stracture GFS-Writer-YYYY-MM-DDTHH-mm-ss.txt

Write an artifact which will backtest for following strategy
1. When Weekly RSI is bouncing back from 60 then Sell a option call spread at 60 RSI Day high price with stop loss of 30%
2. When Weekly RSI is bouncing back from 40 then Sell a option puts spread at 40 RSI Day Low price with stop loss of 30%
Also
1. Show progress bar
2. use end_date = datetime.now()
3. use start_date = end_date - timedelta(days=(365 * 5))
4. use library yfinance, pandas, pandas_ta
5. Console output the date, trades, in and out also other details of each trade
6. Plot it in the chart with candlestick


Write an artifact which scans s&p 500 and 'QQQ' and 'SPY' for following strategy
1. Monthly RSI is at or above 60
2. Weekly RSI is at or above 60
3. Daily RSI is 35 <= daily rsi <= 43
Also
1. for yf.Ticker(symbol) use yf.Ticker(symbol.replace('.', '-'))
2. use exponential weighted moving average (EWM) method for RSI calculation
3. fetch a year's worth of data at once and then resamples it for different timeframes
4. add more robust error handling, particularly in the data fetching process
5. use RSI thresholds (35 <= daily_rsi <= 43)
6. provide more detailed logging, show progress using tqdm library
7. use better error handling for fetching the S&P 500 list and provides feedback on the process
8. seperate each functionality in its own distinct functions, which can be more modular.
9. resamples data for weekly and monthly (use 'ME' instead of 'M') calculations, which can be more accurate, especially for stocks that might not trade every day
10. fetch all data at once and then resample
11. include file output functionality, saving the results with a timestamp
12. handles edge cases better, such as potential issues with fetching the S&P 500 list or insufficient data for certain stocks
13. use end_date = datetime.now()
14. use start_date = end_date - timedelta(days=(365 * 5))
15. use library yfinance, pandas, pandas_ta, plotly

