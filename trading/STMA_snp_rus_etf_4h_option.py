import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from tqdm import tqdm
import os

def get_stock_list():
    stock_list = []

    try:
        with open('data/stock_tickers.txt', 'r') as file:
            stock_list = [line.strip().rstrip(',') for line in file if line.strip()]
    except FileNotFoundError:
        print("data/stock_tickers.txt not found.")

    return stock_list

def get_output_filename():
    """Generate a unique filename based on the current script name and timestamp."""
    script_name = os.path.splitext(os.path.basename(__file__))[0]
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return [f"{script_name}_{timestamp}.txt", f"{script_name}_details_{timestamp}.txt"]

def get_sp500_symbols():
    url = "https://en.wikipedia.org/wiki/List_of_S%26P_500_companies"
    table = pd.read_html(url)[0]
    return table['Symbol'].tolist()

def calculate_supertrend(df, length=100, atr_period=10, multiplier=0.5, ma_type='EMA', change_atr=True):
    def sma(series, periods): return series.rolling(window=periods).mean()
    def ema(series, periods): return series.ewm(span=periods, adjust=False).mean()
    def wma(series, periods):
        weights = np.arange(1, periods + 1)
        return series.rolling(periods).apply(lambda x: np.dot(x, weights) / weights.sum(), raw=True)
    def dema(series, periods):
        ema1 = ema(series, periods)
        ema2 = ema(ema1, periods)
        return 2 * ema1 - ema2
    def tema(series, periods):
        ema1 = ema(series, periods)
        ema2 = ema(ema1, periods)
        return 3 * (ema1 - ema2) + ema(ema2, periods)
    def var_func(series, periods):
        alpha = 2 / (periods + 1)
        ud1 = np.where(series > series.shift(1), series - series.shift(1), 0)
        dd1 = np.where(series < series.shift(1), series.shift(1) - series, 0)
        ud = pd.Series(ud1).rolling(9).sum()
        dd = pd.Series(dd1).rolling(9).sum()
        cmo = (ud - dd) / (ud + dd)
        var = pd.Series(index=series.index)
        var = alpha * np.abs(cmo) * series + (1 - alpha * np.abs(cmo)) * var.shift(1)
        return var.fillna(method='bfill')
    def wwma(series, periods):
        alpha = 1 / periods
        wwma = pd.Series(index=series.index)
        wwma = alpha * series + (1 - alpha) * wwma.shift(1)
        return wwma.fillna(method='bfill')
    def zlema(series, periods):
        lag = (periods - 1) // 2
        ema_data = series + series - series.shift(lag)
        return ema(ema_data, periods)
    def tsf(series, periods):
        x = np.arange(periods)
        x_sum = x.sum()
        x2_sum = (x**2).sum()
        def tsf_calc(y):
            y_sum = y.sum()
            xy_sum = (x * y).sum()
            m = (periods * xy_sum - x_sum * y_sum) / (periods * x2_sum - x_sum**2)
            b = (y_sum - m * x_sum) / periods
            return b + m * periods
        return series.rolling(periods).apply(tsf_calc, raw=True)
    def hull(series, periods):
        return wma(2 * wma(series, periods // 2) - wma(series, periods), int(np.sqrt(periods)))
    def till(series, periods, t3_factor=0.7):
        e1 = ema(series, periods)
        e2 = ema(e1, periods)
        e3 = ema(e2, periods)
        e4 = ema(e3, periods)
        e5 = ema(e4, periods)
        e6 = ema(e5, periods)
        c1 = -t3_factor**3
        c2 = 3 * t3_factor**2 + 3 * t3_factor**3
        c3 = -6 * t3_factor**2 - 3 * t3_factor - 3 * t3_factor**3
        c4 = 1 + 3 * t3_factor + t3_factor**3 + 3 * t3_factor**2
        return c1 * e6 + c2 * e5 + c3 * e4 + c4 * e3

    ma_func = {'SMA': sma, 'EMA': ema, 'WMA': wma, 'DEMA': dema, 'TMA': tema,
               'VAR': var_func, 'WWMA': wwma, 'ZLEMA': zlema, 'TSF': tsf,
               'HULL': hull, 'TILL': till}.get(ma_type, ema)

    df['MA'] = ma_func(df['Close'], length)
    df['TR'] = np.maximum(df['High'] - df['Low'],
                          np.maximum(abs(df['High'] - df['Close'].shift(1)),
                                     abs(df['Low'] - df['Close'].shift(1))))
    df['ATR'] = df['TR'].ewm(span=atr_period, adjust=False).mean() if change_atr else df['TR'].rolling(window=atr_period).mean()

    df['Up'] = df['MA'] - multiplier * df['ATR']
    df['Dn'] = df['MA'] + multiplier * df['ATR']
    df['Trend'] = 0
    df['SuperTrend'] = np.nan

    for i in range(1, len(df)):
        curr, prev = df.index[i], df.index[i-1]
        if df.loc[curr, 'Close'] > df.loc[prev, 'Dn']:
            df.loc[curr, 'Trend'] = 1
        elif df.loc[curr, 'Close'] < df.loc[prev, 'Up']:
            df.loc[curr, 'Trend'] = -1
        else:
            df.loc[curr, 'Trend'] = df.loc[prev, 'Trend']

        if df.loc[curr, 'Trend'] == 1:
            df.loc[curr, 'Up'] = max(df.loc[curr, 'Up'], df.loc[prev, 'Up'])
            df.loc[curr, 'SuperTrend'] = df.loc[curr, 'Up']
        else:
            df.loc[curr, 'Dn'] = min(df.loc[curr, 'Dn'], df.loc[prev, 'Dn'])
            df.loc[curr, 'SuperTrend'] = df.loc[curr, 'Dn']

    return df

def fetch_data(symbol, start_date, end_date, fourHour=False):
    if fourHour == True:
        # Fetch 1-hour data and resample to 4-hour timeframe
        data = yf.download(symbol, start=start_date, end=end_date, interval='1h', progress=False)
        data_4h = data.resample('4h').agg({
            'Open': 'first',
            'High': 'max',
            'Low': 'min',
            'Close': 'last',
            'Volume': 'sum'
        })
        return data_4h.dropna()
    else:
        return yf.download(symbol, start=start_date, end=end_date, progress=False)

def check_trend_reversal(symbol):
    end_date = datetime.now()
    start_date = end_date - timedelta(days=300)  # Get 300 days of data to have enough history

    try:
        data = fetch_data(symbol.replace('.', '-'), start_date, end_date, fourHour=True)
        if len(data) < 30:  # Ensure we have enough data points
            return None

        data = calculate_supertrend(data)

        # Check for trend reversal in the last week
        last_week = data.iloc[-7:]
        if (last_week['Trend'].iloc[0] != last_week['Trend'].iloc[-1] and
            last_week['Trend'].iloc[-1] != last_week['Trend'].iloc[-2]):
            reversal_date = last_week.index[-1].strftime('%Y-%m-%d')
            return (symbol, reversal_date)
    except Exception as e:
        print(f"Error processing {symbol}: {str(e)}")

    return None

def main():
    # symbols = get_sp500_symbols() + ['QQQ', 'SPY']
    symbols = get_stock_list()
    results = []

    print("Scanning for trend reversals...")
    for symbol in tqdm(symbols):
        result = check_trend_reversal(symbol)
        if result:
            results.append(result)

    if results:
        print("\nStocks with trend reversals in the last week:")
        for symbol, date in results:
            print(f"{symbol}: Reversal on {date}")

        # Save results to a dynamically named file
        output_filenames = get_output_filename()
        with open(output_filenames[0], 'w') as f:
            for symbol, date in results:
                f.write(f"{symbol}\n")

        with open(output_filenames[1], 'w') as f:
            for symbol, date in results:
                f.write(f"{symbol}: Reversal on {date}\n")
    else:
        print("\nNo trend reversals found in the last week.")

if __name__ == "__main__":
    main()