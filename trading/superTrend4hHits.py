import yfinance as yf
import pandas as pd
import numpy as np
from tqdm import tqdm
from datetime import datetime, timedelta
import os

def get_output_filename():
    """Generate a unique filename based on the current script name and timestamp."""
    script_name = os.path.splitext(os.path.basename(__file__))[0]
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return [f"{script_name}_{timestamp}.txt", f"{script_name}_details_{timestamp}.txt"]

def supertrend(data, factor=3, period=22):
    # Calculate True Range
    data['TR'] = np.maximum(data['High'] - data['Low'],
                            np.maximum(abs(data['High'] - data['Close'].shift(1)),
                                       abs(data['Low'] - data['Close'].shift(1))))

    hl2 = (data['High'] + data['Low']) / 2
    atr = data['TR'].rolling(period).mean()

    upper_basic = hl2 + (factor * atr)
    lower_basic = hl2 - (factor * atr)

    upper_band = pd.Series(index=data.index)
    lower_band = pd.Series(index=data.index)
    supertrendSeries = pd.Series(index=data.index)
    direction = pd.Series(index=data.index)

    for i in range(period, len(data)):
        if i == period:
            upper_band.iloc[i] = upper_basic.iloc[i]
            lower_band.iloc[i] = lower_basic.iloc[i]
            supertrendSeries.iloc[i] = upper_band.iloc[i] if data['Close'].iloc[i] <= upper_band.iloc[i] else lower_band.iloc[i]
            direction.iloc[i] = 1 if supertrendSeries.iloc[i] == lower_band.iloc[i] else -1
        else:
            upper_band.iloc[i] = upper_basic.iloc[i] if (upper_basic.iloc[i] < upper_band.iloc[i-1] or data['Close'].iloc[i-1] > upper_band.iloc[i-1]) else upper_band.iloc[i-1]
            lower_band.iloc[i] = lower_basic.iloc[i] if (lower_basic.iloc[i] > lower_band.iloc[i-1] or data['Close'].iloc[i-1] < lower_band.iloc[i-1]) else lower_band.iloc[i-1]

            if supertrendSeries.iloc[i-1] == upper_band.iloc[i-1]:
                supertrendSeries.iloc[i] = upper_band.iloc[i] if data['Close'].iloc[i] <= upper_band.iloc[i] else lower_band.iloc[i]
            else:
                supertrendSeries.iloc[i] = lower_band.iloc[i] if data['Close'].iloc[i] >= lower_band.iloc[i] else upper_band.iloc[i]

            direction.iloc[i] = 1 if supertrendSeries.iloc[i] == lower_band.iloc[i] else -1

    return pd.DataFrame({'SuperTrendSeries': supertrendSeries, 'Direction': direction}, index=data.index)

def get_sp500_symbols():
    url = "https://en.wikipedia.org/wiki/List_of_S%26P_500_companies"
    tables = pd.read_html(url)
    df = tables[0]
    return df['Symbol'].tolist()

def resample_data(data, timeframe):
    if timeframe == '4h':
        resampled = data.resample('4h').agg({
            'Open': 'first',
            'High': 'max',
            'Low': 'min',
            'Close': 'last',
            'Volume': 'sum'
        }).dropna()
    else:
        resampled = data
    return resampled

def screen_stocks(timeframe='1h', lookback_period='7d', reversal_window=7):
    symbols = get_sp500_symbols() + ['SPY', 'QQQ']
    results = []

    end_date = datetime.today()
    start_date = end_date - timedelta(days=365)  # Fetch 7 days of hourly data

    for symbol in tqdm(symbols, desc="Screening stocks"):
        try:
            stock = yf.Ticker(symbol.replace(".", "-"))
            data = stock.history(interval='1h', start=start_date, end=end_date)

            if len(data) < reversal_window:
                continue

            data = resample_data(data, timeframe)
            trend = supertrend(data)

            recent_trend = trend['Direction'].tail(reversal_window)
            if 1 in recent_trend.values and -1 in recent_trend.values:
                reversal_index = None
                reversal_date = None
                reversal_type = None

                if recent_trend.iloc[-1] == 1 and -1 in recent_trend.iloc[:-1].values:
                    reversal_type = "Bullish Reversal"
                    reversal_index = recent_trend[recent_trend == 1].index[0]
                elif recent_trend.iloc[-1] == -1 and 1 in recent_trend.iloc[:-1].values:
                    reversal_type = "Bearish Reversal"
                    reversal_index = recent_trend[recent_trend == -1].index[0]

                if reversal_index is not None:
                    reversal_date = reversal_index
                    reversal_position = trend.index.get_loc(reversal_index)
                    if isinstance(reversal_position, slice):
                        reversal_position = reversal_position.start
                    results.append((symbol, reversal_type, reversal_position, reversal_date))

        except Exception as e:
            print(f"Error processing {symbol}: {str(e)}")

    return results

if __name__ == "__main__":
    # Choose timeframe: '1h' for 1-hour, '4h' for 4-hour (resampled from 1h data)
    timeframe = '4h'
    lookback_period = '7d'
    reversal_window = 42  # Approximately 1 week for 4-hour data (6 periods per day * 7 days)

    results = screen_stocks(timeframe, lookback_period, reversal_window)

    if len(results) > 0:
        output_filenames = get_output_filename()
        with open(output_filenames[0], 'w') as f:
            for symbol, _, _, _ in results:
                f.write(f"{symbol}\n")

        with open(output_filenames[1], 'w') as f:
            print(f"\nStocks with SuperTrend reversals in the last {reversal_window} {timeframe} periods:")
            for symbol, reversal_type, reversal_index, reversal_date in results:
                print(f"{symbol}: {reversal_type} at index {reversal_index}, date {reversal_date}")
                f.write(f"{symbol}: {reversal_type} at index {reversal_index}, date {reversal_date}\n")

        print(f"\nResults saved to {output_filenames}")
    else:
        print("No reversals found in the specified timeframe.")