import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from tqdm import tqdm
import os

# ticker_file_path = 'data/stock_tickers.txt'
ticker_file_path = 'data/snp500_tickers.txt'

def get_stock_list():
    """Read stock tickers from a file."""
    stock_list = []
    try:
        with open(ticker_file_path, 'r') as file:
            stock_list = [line.strip().rstrip(',') for line in file if line.strip()]
    except FileNotFoundError:
        print(f"{ticker_file_path} not found.")
    return stock_list

def get_output_filename():
    """Generate unique filenames based on current script name and timestamp."""
    script_name = os.path.splitext(os.path.basename(__file__))[0]
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    scannerResults_directory = f"{os.path.expanduser('~')}/Downloads/trading/scannerResults"
    os.makedirs(scannerResults_directory, exist_ok=True)
    return [
        f"{scannerResults_directory}/{script_name}_{timestamp}.txt",
        f"{scannerResults_directory}/{script_name}_details_{timestamp}.txt"
    ]

def fetch_custom_data(symbol, period='4h'):
    """Fetch and resample data for the given symbol."""
    end_date = datetime.now()

    match period:
        case '4h':
            start_date = end_date - timedelta(days=60)  # Get 60 days of data
            data = yf.download(symbol, start=start_date, end=end_date, interval='1h', progress=False)
            data_4h = data.resample('4h').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            })
            return data_4h.dropna()
        case 'D':
            start_date = end_date - timedelta(days=100)  # Get 100 days of daily data
            return yf.download(symbol, start=start_date, end=end_date, interval='1d', progress=False)
        case _:
            start_date = end_date - timedelta(days=100)
            return yf.download(symbol, start=start_date, end=end_date, progress=False)

def detect_engulfing_pattern(data):
    """Detect engulfing patterns in the price data."""
    df = data.copy()

    # Calculate current and previous candle properties
    df['prev_open'] = df['Open'].shift(1)
    df['prev_close'] = df['Close'].shift(1)
    df['prev2_open'] = df['Open'].shift(2)
    df['prev2_close'] = df['Close'].shift(2)

    # Detect previous candles engulfing patterns
    df['prev_bullish_engulfing'] = (
        (df['prev_open'] < df['prev_close']) &
        (df['prev2_open'] > df['prev2_close']) &
        (df['prev_open'] <= df['prev2_close']) &
        (df['prev_close'] >= df['prev2_open'])
    )

    df['prev_bearish_engulfing'] = (
        (df['prev_open'] > df['prev_close']) &
        (df['prev2_open'] < df['prev2_close']) &
        (df['prev_open'] >= df['prev2_close']) &
        (df['prev_close'] <= df['prev2_open'])
    )

    # Detect current engulfing patterns
    df['bullish_engulfing'] = (
        (df['Open'] < df['Close']) &
        (df['prev_open'] > df['prev_close']) &
        (df['Open'] <= df['prev_close']) &
        (df['Close'] >= df['prev_open']) &
        df['prev_bearish_engulfing']
    )

    df['bearish_engulfing'] = (
        (df['Open'] > df['Close']) &
        (df['prev_open'] < df['prev_close']) &
        (df['Open'] >= df['prev_close']) &
        (df['Close'] <= df['prev_open']) &
        df['prev_bullish_engulfing']
    )

    return df

def calculate_position_size(entry_price, stop_loss, risk_per_trade=100):
    """Calculate position size based on risk management parameters."""
    risk_per_share = abs(entry_price - stop_loss)
    num_shares = risk_per_trade / risk_per_share
    return round(num_shares)

def check_engulfing_pattern(symbol):
    """Check for engulfing patterns for a given symbol."""
    try:
        data = fetch_custom_data(symbol.replace('.', '-'), period='1h')
        if len(data) < 10:  # Ensure we have enough data points
            return None

        data = detect_engulfing_pattern(data)

        # Check for engulfing pattern in the last 3 candles
        last_candles = data.iloc[-3:]
        if last_candles['bullish_engulfing'].iloc[-1] or last_candles['bearish_engulfing'].iloc[-1]:

            pattern_type = 'Bullish' if last_candles['bullish_engulfing'].iloc[-1] else 'Bearish'
            entry_price = round(last_candles['Close'].iloc[-1], 2)

            # Set stop loss based on pattern type
            if pattern_type == 'Bullish':
                stop_loss = round(min(last_candles['Low'].min(), last_candles['Low'].iloc[-1]), 2)
                target_price = round(entry_price + 3 * (entry_price - stop_loss), 2)
            else:
                stop_loss = round(max(last_candles['High'].max(), last_candles['High'].iloc[-1]), 2)
                target_price = round(entry_price - 3 * (stop_loss - entry_price), 2)

            # Calculate position size
            num_shares = calculate_position_size(entry_price, stop_loss)

            # Calculate entry cost and potential profit/loss
            entry_cost = entry_price * num_shares
            exit_price = target_price * num_shares
            estimated_profit = abs(exit_price - entry_cost)
            estimated_loss = abs(entry_cost - (stop_loss * num_shares))

            return (
                symbol,
                last_candles.index[-1].strftime('%Y-%m-%d %H:%M'),
                pattern_type,
                entry_price,
                stop_loss,
                target_price,
                num_shares,
                round(entry_cost, 2),
                round(exit_price, 2),
                round(estimated_profit, 2),
                round(estimated_loss, 2),
                last_candles['Volume'].sum()
            )

    except Exception as e:
        print(f"Error processing {symbol}: {str(e)}")

    return None

def main():
    symbols = get_stock_list()
    results = []

    print("Scanning for engulfing patterns...")
    for symbol in tqdm(symbols):
        result = check_engulfing_pattern(symbol)
        if result:
            results.append(result)

    if results:
        print("\nStocks with engulfing patterns:")
        for (symbol, date, pattern_type, entry_price, stop_loss, target_price,
             num_shares, entry_cost, exit_price, estimated_profit,
             estimated_loss, volume) in results:
            print(f"{symbol}: {pattern_type} Engulfing on {date}")
            print(f"Entry: ${entry_price}, Stop: ${stop_loss}, Target: ${target_price}")
            print(f"Shares: {num_shares}, Entry Cost: ${entry_cost}")
            print(f"Est. Profit: ${estimated_profit}, Est. Loss: ${estimated_loss}")
            print(f"Volume: {volume}\n")

        # Save results to files
        output_filenames = get_output_filename()
        with open(output_filenames[0], 'w') as f:
            for data in results:
                f.write(f"{data[0]}\n")

        with open(output_filenames[1], 'w') as f:
            for data in results:
                f.write(f"{data[0]}: {data[2]} Engulfing on {data[1]}\n")
                f.write(f"Entry: ${data[3]}, Stop: ${data[4]}, Target: ${data[5]}\n")
                f.write(f"Shares: {data[6]}, Entry Cost: ${data[7]}\n")
                f.write(f"Est. Profit: ${data[9]}, Est. Loss: ${data[10]}\n")
                f.write(f"Volume: {data[11]}\n\n")

        print(f"\nResults saved to {output_filenames}")
    else:
        print("\nNo engulfing patterns found.")

if __name__ == "__main__":
    main()