import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime, timedelta
from tqdm import tqdm
import os

def get_stock_list():
    stock_list = []

    try:
        with open('data/stock_tickers.txt', 'r') as file:
            stock_list = [line.strip().rstrip(',') for line in file if line.strip()]
    except FileNotFoundError:
        print("data/stock_tickers.txt not found.")

    return stock_list

def get_output_filename():
    """Generate a unique filename based on the current script name and timestamp."""
    script_name = os.path.splitext(os.path.basename(__file__))[0]
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    scannerResults_directory = f"{os.path.expanduser('~')}/Downloads/trading/scannerResults"
    os.makedirs(scannerResults_directory, exist_ok=True)
    return [f"{scannerResults_directory}/{script_name}_{timestamp}.txt", f"{scannerResults_directory}/{script_name}_details_{timestamp}.txt"]

def fetch_custom_data(symbol, period='15m'):
    end_date = datetime.now()

    match period:
        case '15m':
            start_date = end_date - timedelta(days=600)
            data = yf.download(symbol, start=start_date, end=end_date, interval='1m', progress=False)
            data_1m = data.resample('15m').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            })
            # Convert column names to lowercase before returning
            data_1m.columns = data_1m.columns.str.lower()
            return data_1m.dropna()
        case '1h':
            start_date = end_date - timedelta(days=600)
            data = yf.download(symbol, start=start_date, end=end_date, interval='1h', progress=False)
            data_1h = data.resample('1h').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            })
            data_1h.columns = data_1h.columns.str.lower()
            return data_1h.dropna()
        case '4h':
            start_date = end_date - timedelta(days=600)
            data = yf.download(symbol, start=start_date, end=end_date, interval='1h', progress=False)
            data_4h = data.resample('4h').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            })
            data_4h.columns = data_4h.columns.str.lower()
            return data_4h.dropna()
        case 'W':
            start_date = end_date - timedelta(days=1826)
            data = yf.download(symbol, start=start_date, end=end_date, interval='1d', progress=False)
            data_weekly = data.resample('W').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            })
            data_weekly.columns = data_weekly.columns.str.lower()
            return data_weekly.dropna()
        case 'ME':
            start_date = end_date - timedelta(days=1826)
            data = yf.download(symbol, start=start_date, end=end_date, interval='1d', progress=False)
            data_monthly = data.resample('ME').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            })
            data_monthly.columns = data_monthly.columns.str.lower()
            return data_monthly.dropna()
        case _:
            start_date = end_date - timedelta(days=1826)
            data = yf.download(symbol, start=start_date, end=end_date, progress=False)
            data.columns = data.columns.str.lower()
            return data

class ZLSMAWithChandelierExit:
    def __init__(self,
                 zlsma_length=50,
                 zlsma_offset=0,
                 ce_atr_period=22,
                 ce_atr_multiplier=3.0,
                 ce_use_close=True,
                 min_price_change_pct: float = 10.0,
                 min_volume: int = 100000):  # Added min_volume parameter
        """
        Initialize the ZLSMA with Chandelier Exit indicator

        Parameters:
        -----------
        zlsma_length : int
            Length for ZLSMA calculation (default: 50)
        zlsma_offset : int
            Offset for ZLSMA calculation (default: 0)
        ce_atr_period : int
            ATR period for Chandelier Exit (default: 22)
        ce_atr_multiplier : float
            ATR multiplier for Chandelier Exit (default: 3.0)
        ce_use_close : bool
            Use close price for extremums in Chandelier Exit (default: True)
        min_price_change_pct : float
            Minimum price change percentage required (default: 10.0)
        min_volume : int
            Minimum average daily volume required (default: 100000)
        """
        self.zlsma_length = zlsma_length
        self.zlsma_offset = zlsma_offset
        self.ce_atr_period = ce_atr_period
        self.ce_atr_multiplier = ce_atr_multiplier
        self.ce_use_close = ce_use_close
        self.min_price_change_pct = min_price_change_pct
        self.min_volume = min_volume

    def linear_regression(self, series, length):
        """Calculate linear regression"""
        x = np.arange(len(series))
        x = x[-length:]
        y = series.values[-length:]

        if len(y) < length:
            return np.nan

        coef = np.polyfit(x, y, 1)
        return coef[0] * x[-1] + coef[1]

    def calculate_zlsma(self, df):
        """Calculate Zero-Lag Simple Moving Average"""
        close = df['close'].copy()

        # Calculate first LSMA
        lsma = pd.Series(index=df.index, dtype='float64')
        for i in range(self.zlsma_length - 1, len(df)):
            lsma.iloc[i] = self.linear_regression(
                close.iloc[i - self.zlsma_length + 1:i + 1],
                self.zlsma_length
            )

        # Calculate second LSMA
        lsma2 = pd.Series(index=df.index, dtype='float64')
        for i in range(self.zlsma_length - 1, len(df)):
            lsma2.iloc[i] = self.linear_regression(
                lsma.iloc[i - self.zlsma_length + 1:i + 1],
                self.zlsma_length
            )

        # Calculate final ZLSMA
        eq = lsma - lsma2
        zlsma = lsma + eq

        return zlsma

    def calculate_atr(self, df, period):
        """Calculate Average True Range (ATR)"""
        high = df['high']
        low = df['low']
        close = df['close']

        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())

        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(window=period).mean()

        return atr

    def calculate_chandelier_exit(self, df):
        """Calculate Chandelier Exit signals"""
        close = df['close']
        high = df['high']
        low = df['low']

        # Calculate ATR
        atr = self.calculate_atr(df, self.ce_atr_period) * self.ce_atr_multiplier

        # Calculate stops
        if self.ce_use_close:
            highest = close.rolling(window=self.ce_atr_period).max()
            lowest = close.rolling(window=self.ce_atr_period).min()
        else:
            highest = high.rolling(window=self.ce_atr_period).max()
            lowest = low.rolling(window=self.ce_atr_period).min()

        long_stop = highest - atr
        short_stop = lowest + atr

        # Initialize direction series
        direction = pd.Series(index=df.index, dtype='int')
        direction.iloc[0] = 1  # Start with long position

        # Calculate direction changes
        for i in range(1, len(df)):
            prev_dir = direction.iloc[i-1]
            prev_long_stop = long_stop.iloc[i-1]
            prev_short_stop = short_stop.iloc[i-1]
            curr_close = close.iloc[i]

            if curr_close > prev_short_stop:
                direction.iloc[i] = 1
            elif curr_close < prev_long_stop:
                direction.iloc[i] = -1
            else:
                direction.iloc[i] = prev_dir

        # Generate signals
        buy_signal = (direction == 1) & (direction.shift(1) == -1)
        sell_signal = (direction == -1) & (direction.shift(1) == 1)

        return {
            'long_stop': long_stop,
            'short_stop': short_stop,
            'direction': direction,
            'buy_signal': buy_signal,
            'sell_signal': sell_signal
        }

    def calculate(self, df):
        """
        Calculate all indicators

        Parameters:
        -----------
        df : pandas.DataFrame
            DataFrame with 'open', 'high', 'low', 'close' columns

        Returns:
        --------
        dict
            Dictionary containing all calculated indicators and signals
        """
        # Calculate ZLSMA
        zlsma = self.calculate_zlsma(df)

        # Calculate Chandelier Exit
        ce_signals = self.calculate_chandelier_exit(df)

        return {
            'zlsma': zlsma,
            **ce_signals
        }

    def has_minimum_requirements(self, df: pd.DataFrame) -> tuple[bool, str]:
        """
        Check if the stock meets minimum requirements for price change and volume

        Args:
            df: DataFrame with OHLC data

        Returns:
            tuple: (bool, str) - (meets requirements, reason if not)
        """
        # Convert column names to lowercase for consistency
        df.columns = df.columns.str.lower()

        # Check price change
        first_price = df['close'].iloc[0]
        last_price = df['close'].iloc[-1]
        price_change_pct = abs((last_price - first_price) / first_price * 100)

        # Check for significant moves within the period
        high_price = df['high'].max()
        low_price = df['low'].min()
        max_price_change_pct = abs((high_price - low_price) / low_price * 100)

        # Check average volume
        avg_volume = df['volume'].mean()

        if avg_volume < self.min_volume:
            return False, f"Insufficient volume (avg: {int(avg_volume):,})"

        if price_change_pct < self.min_price_change_pct and max_price_change_pct < self.min_price_change_pct:
            return False, f"Insufficient price movement ({max(price_change_pct, max_price_change_pct):.1f}%)"

        return True, "Meets requirements"

def scan_stocks(period: str = "1y",
               interval: str = "1d",
               min_price_change_pct: float = 10.0,
               min_volume: int = 100000) -> list:
    """
    Scan multiple stocks for ZLSMA with Chandelier Exit signals

    Args:
        period: Data period to download
        interval: Data interval
        min_price_change_pct: Minimum price change percentage required
        min_volume: Minimum average daily volume required
    """
    indicator = ZLSMAWithChandelierExit(
        min_price_change_pct=min_price_change_pct,
        min_volume=min_volume
    )
    signals = []
    symbols = get_stock_list()

    # Track rejected symbols and reasons
    rejected_symbols = []

    for symbol in tqdm(symbols):
        try:
            # Download data
            df = fetch_custom_data(symbol.replace('.', '-'), period='4h')

            if len(df) == 0:
                rejected_symbols.append((symbol, "No data available"))
                continue

            # Convert column names to lowercase immediately after fetching
            df.columns = df.columns.str.lower()

            # Check if stock meets minimum requirements
            meets_req, reason = indicator.has_minimum_requirements(df)
            if not meets_req:
                rejected_symbols.append((symbol, reason))
                continue

            # Calculate indicators and get signals
            results = indicator.calculate(df)

            # Check for the most recent signals
            if len(df) >= 2:
                last_buy = results['buy_signal'].iloc[-1]
                last_sell = results['sell_signal'].iloc[-1]
                timestamp = df.index[-1]
                last_price = df['close'].iloc[-1]
                last_direction = results['direction'].iloc[-1]
                avg_volume = df['volume'].mean()  # Calculate average volume

                # Check if we have a signal in the last bar
                if last_buy:
                    signals.append((
                        symbol,
                        "BUY",
                        timestamp,
                        f"{last_price:.2f}",
                        f"{results['zlsma'].iloc[-1]:.2f}",
                        f"{results['long_stop'].iloc[-1]:.2f}",
                        f"{int(avg_volume):,}"  # Add volume info
                    ))
                elif last_sell:
                    signals.append((
                        symbol,
                        "SELL",
                        timestamp,
                        f"{last_price:.2f}",
                        f"{results['zlsma'].iloc[-1]:.2f}",
                        f"{results['short_stop'].iloc[-1]:.2f}",
                        f"{int(avg_volume):,}"  # Add volume info
                    ))

        except Exception as e:
            rejected_symbols.append((symbol, f"Error: {str(e)}"))

    return signals, rejected_symbols

if __name__ == "__main__":
    print("Scanning stocks for ZLSMA with Chandelier Exit signals...")
    results, rejected = scan_stocks(min_price_change_pct=10.0, min_volume=50000)

    # Save results to dynamically named files
    output_filenames = get_output_filename()

    if results:
        print(f"\nFound {len(results)} signals.")

        # Save simple ticker list
        with open(output_filenames[0], 'w') as f:
            for symbol, *_ in results:
                f.write(f"{symbol}\n")

        # Save detailed results
        with open(output_filenames[1], 'w') as f:
            # Write header
            f.write("=== SIGNALS ===\n\n")

            # Write signals
            for symbol, signal_type, timestamp, price, zlsma, stop, volume in results:
                f.write(
                    f"{symbol}: {signal_type} signal at {timestamp}\n"
                    f"Price: ${price}, ZLSMA: ${zlsma}, "
                    f"{'Long' if signal_type == 'BUY' else 'Short'} Stop: ${stop}\n"
                    f"Average Volume: {volume} shares\n"
                    f"{'-' * 60}\n"
                )

            # Write rejected symbols
            f.write("\n=== REJECTED SYMBOLS ===\n\n")
            for symbol, reason in rejected:
                f.write(f"{symbol}: {reason}\n")

        # Print summary to console
        print(f"Found {len(results)} signals and {len(rejected)} rejected symbols.")
        print(f"Results saved to:")
        print(f"Ticker list: {output_filenames[0]}")
        print(f"Detailed results: {output_filenames[1]}")
    else:
        print("\nNo signals found meeting the criteria.")