# insider_bar_ups_only.py
import pandas as pd
import yfinance as yf
from datetime import datetime, timedelta
from tqdm import tqdm
import os, sys

def get_output_filename():
    """Generate a unique filename based on the current script name and timestamp."""
    script_name = os.path.splitext(os.path.basename(__file__))[0]
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    scannerResults_directory = f"{os.path.expanduser('~')}/Downloads/trading/scannerResults"
    os.makedirs(scannerResults_directory, exist_ok=True)
    return [f"{scannerResults_directory}/{script_name}_{timestamp}.txt", f"{scannerResults_directory}/{script_name}_details_{timestamp}.txt"]

def get_stock_list():
    # stock_list = ['A', 'AAL', 'AAON', 'AAPL']
    stock_list = []

    try:
        with open('data/stock_tickers.txt', 'r') as file:
            stock_list = [line.strip().rstrip(',') for line in file if line.strip()]
    except FileNotFoundError:
        print("data/stock_tickers.txt not found.")

    return stock_list

def fetch_custom_data(symbol, period='15m'):
    end_date = datetime.now()

    match period:
        case '15m':
            start_date = end_date - timedelta(days=600)  # Get 600 days of data to have enough history
            data = yf.download(symbol, start=start_date, end=end_date, interval='1m', progress=False)
            data_1m = data.resample('15m').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            })
            return data_1m.dropna()
        case '1h':
            start_date = end_date - timedelta(days=600)  # Get 600 days of data to have enough history
            # Fetch 1-hour data and resample to 1-hour timeframe
            data = yf.download(symbol, start=start_date, end=end_date, interval='1h', progress=False)
            data_1h = data.resample('1h').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            })
            return data_1h.dropna()
        case '4h':
            start_date = end_date - timedelta(days=600)  # Get 600 days of data to have enough history
            # Fetch 1-hour data and resample to 4-hour timeframe
            data = yf.download(symbol, start=start_date, end=end_date, interval='1h', progress=False)
            # print(f'resamped: {data.resample('4h')}')
            data_4h = data.resample('4h').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            })
            # print(f"==>> data_4h: {data_4h.iloc[0].name}")
            return data_4h.dropna()
        case 'W':
            start_date = end_date - timedelta(days=1826)  # Get 5 years of data to have enough history
            # Fetch 1-hour data and resample to 4-hour timeframe
            data = yf.download(symbol, start=start_date, end=end_date, interval='1d', progress=False)
            data_weekly = data.resample('W').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            })
            return data_weekly.dropna()
        case 'ME':
            start_date = end_date - timedelta(days=1826)  # Get 5 years of data to have enough history
            # Fetch 1-hour data and resample to 4-hour timeframe
            data = yf.download(symbol, start=start_date, end=end_date, interval='1d', progress=False)
            data_monthly = data.resample('ME').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            })
            return data_monthly.dropna()
        case _:
            start_date = end_date - timedelta(days=1826)
            return yf.download(symbol, start=start_date, end=end_date, progress=False)

def calculate_position_size(entry_price, stop_loss, risk_per_trade=100):
    risk_per_share = abs(entry_price - stop_loss)
    num_shares = risk_per_trade / risk_per_share
    return round(num_shares)

def check_inside_bar_conditions(df, lookback=3, volume_threshold=100000, close_threshold=100):
    """Check for inside bar conditions in the given dataframe."""
    df_len = len(df)
    for i in range(df_len - 1, 0, -1):
        current_day = df.iloc[i]
        previous_day = df.iloc[i-1]

        condition1 = current_day['High'] < previous_day['High']
        condition2 = current_day['Low'] > previous_day['Low']
        condition3 = current_day['Volume'] > volume_threshold
        condition4 = current_day['Close'] > close_threshold

        if condition1 and condition2 and condition3 and condition4:
            for j in range(i+1, min(i+lookback+1, df_len)):
                if df.iloc[j]['Close'] > current_day['High']:
                    entry_price = round(df.iloc[-1]['Close'], 2)
                    stop_loss = round(previous_day['Low'], 2)
                    target_price = round(entry_price + 3 * (entry_price - stop_loss), 2)  # 1:3 risk-reward ratio
                    num_shares = calculate_position_size(entry_price, stop_loss)
                    entry_cost = entry_price * num_shares
                    exit_price = target_price * num_shares
                    estimated_profit = exit_price - entry_cost
                    estimated_loss = entry_cost - (stop_loss * num_shares)
                    return True, df_len - (i - 1), round(entry_price, 2), round(stop_loss, 2), round(target_price, 2), num_shares, round(entry_cost, 2), round(exit_price, 2), round(estimated_profit, 2), round(estimated_loss, 2), current_day.name

    return False, -1, '', '', '', '', '', '', '', '', ''

def inside_bar_hits(ticker):
    results = []
    try:
        lookback = 5
        data = fetch_custom_data(ticker.replace('.', '-'), period='4h')
        if len(data) < 34:
            return None

        # Check conditions
        passed, position, entry_price, stop_loss, target_price, num_shares, entry_cost, exit_price, estimated_profit, estimated_loss, dtime = check_inside_bar_conditions(data, lookback)

        if passed:
            return (ticker, position, entry_price, stop_loss, target_price, num_shares, entry_cost, exit_price, estimated_profit, estimated_loss, dtime)
            # pbar.set_description(f"Found: {ticker} at position {position}")

    except Exception as e:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        print(exc_type, fname, exc_tb.tb_lineno)
        print(f"Error processing {ticker}: {str(e)}")

def main():
    # Get Stock tickers
    symbols = get_stock_list()

    # Initialize results list
    results = []

    # Create progress bar
    pbar = tqdm(symbols, desc="Screening for Inside Bars in stocks")

    # Screen stocks
    for ticker in pbar:
        try:
            results.append(inside_bar_hits(ticker))

        except Exception as e:
            print(f"Error processing {ticker}: {str(e)}")

    results = [x for x in results if x is not None]
    print(f"\nStocks passing the screen: {len(results)}")
    try:
        for ticker, position, entry_price, stop_loss, target_price, num_shares, entry_cost, exit_price, estimated_profit, estimated_loss, dtime in results:
            print(f"{ticker} (Position: {position} ({dtime})) entry_price: {entry_price}, stop_loss: {stop_loss}, target_price: {target_price}, num_shares: {num_shares}, entry_cost: {entry_cost}, exit_price: {exit_price}, estimated_profit: {estimated_profit}, estimated_loss: {estimated_loss}")

    except Exception as e:
        print(e)

    # Save results to a dynamically named file
    output_filenames = get_output_filename()

    with open(output_filenames[0], 'w') as f:
        try:
            for ticker, position, entry_price, stop_loss, target_price, num_shares, entry_cst, exit_price, estimated_profit, estimated_loss, dtime in results:
                if (position <= 5):
                    f.write(f"{ticker}\n")

        except Exception as e:
            print(e)

    with open(output_filenames[1], 'w') as f:
        def sortSecond(val):
            return val[6]

        results.sort(key=sortSecond)

        try:
            for ticker, position, entry_price, stop_loss, target_price, num_shares, entry_cost, exit_price, estimated_profit, estimated_loss, dtime in results:
                f.write(f"{ticker} (Position: {position} ({dtime})) entry_cost: {entry_cost}, num_shares: {num_shares}, estimated_loss: {estimated_loss}, entry_price: {entry_price}, stop_loss: {stop_loss}, target_price: {target_price}, exit_price: {exit_price}, estimated_profit: {estimated_profit}\n")

        except Exception as e:
            print(e)

    print(f"\nResults saved to {output_filenames}")

if __name__ == "__main__":
    main()