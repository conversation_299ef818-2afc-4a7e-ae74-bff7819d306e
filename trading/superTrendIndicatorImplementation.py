import yfinance as yf
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, <PERSON><PERSON><PERSON>

def calculate_supertrend(data, period=22, multiplier=3):
    hl2 = (data['High'] + data['Low']) / 2
    atr = data['TR'].rolling(period).mean()

    upper_basic = hl2 + (multiplier * atr)
    lower_basic = hl2 - (multiplier * atr)

    upper_band = pd.Series(index=data.index)
    lower_band = pd.Series(index=data.index)
    supertrend = pd.Series(index=data.index)
    direction = pd.Series(index=data.index)

    for i in range(period, len(data)):
        if i == period:
            upper_band.iloc[i] = upper_basic.iloc[i]
            lower_band.iloc[i] = lower_basic.iloc[i]
            supertrend.iloc[i] = upper_band.iloc[i] if data['Close'].iloc[i] <= upper_band.iloc[i] else lower_band.iloc[i]
            direction.iloc[i] = 1 if supertrend.iloc[i] == lower_band.iloc[i] else -1
        else:
            upper_band.iloc[i] = upper_basic.iloc[i] if (upper_basic.iloc[i] < upper_band.iloc[i-1] or data['Close'].iloc[i-1] > upper_band.iloc[i-1]) else upper_band.iloc[i-1]
            lower_band.iloc[i] = lower_basic.iloc[i] if (lower_basic.iloc[i] > lower_band.iloc[i-1] or data['Close'].iloc[i-1] < lower_band.iloc[i-1]) else lower_band.iloc[i-1]

            if supertrend.iloc[i-1] == upper_band.iloc[i-1]:
                supertrend.iloc[i] = upper_band.iloc[i] if data['Close'].iloc[i] <= upper_band.iloc[i] else lower_band.iloc[i]
            else:
                supertrend.iloc[i] = lower_band.iloc[i] if data['Close'].iloc[i] >= lower_band.iloc[i] else upper_band.iloc[i]

            direction.iloc[i] = 1 if supertrend.iloc[i] == lower_band.iloc[i] else -1

    return pd.DataFrame({'SuperTrend': supertrend, 'Direction': direction}, index=data.index)

def plot_supertrend(data, supertrend, ticker):
    fig = make_subplots(rows=2, cols=1, shared_xaxes=True,
                        vertical_spacing=0.03, subplot_titles=(f'{ticker} Price', 'Volume'),
                        row_heights=[0.7, 0.3])

    # Candlestick chart for pricing
    fig.add_trace(go.Candlestick(x=data.index, open=data['Open'], high=data['High'],
                                 low=data['Low'], close=data['Close'], name='Price'),
                  row=1, col=1)

    # SuperTrend
    fig.add_trace(go.Scatter(x=supertrend.index, y=supertrend['SuperTrend'],
                             line=dict(color='grey', width=2),
                             name='SuperTrend'),
                  row=1, col=1)

    # Color the SuperTrend line
    colors = np.where(supertrend['Direction'] == 1, 'green', 'red')
    for i in range(len(supertrend) - 1):
        fig.add_trace(go.Scatter(x=[supertrend.index[i], supertrend.index[i+1]],
                                 y=[supertrend['SuperTrend'].iloc[i], supertrend['SuperTrend'].iloc[i+1]],
                                 line=dict(color=colors[i], width=2),
                                 showlegend=False),
                      row=1, col=1)

    # Buy and Sell signals
    buy_signals = (supertrend['Direction'] == 1) & (supertrend['Direction'].shift(1) == -1)
    sell_signals = (supertrend['Direction'] == -1) & (supertrend['Direction'].shift(1) == 1)

    fig.add_trace(go.Scatter(x=supertrend.index[buy_signals], y=data.loc[supertrend.index[buy_signals], 'Low'],
                             mode='markers', marker=dict(symbol='triangle-up', size=10, color='green'),
                             name='Buy Signal'),
                  row=1, col=1)
    fig.add_trace(go.Scatter(x=supertrend.index[sell_signals], y=data.loc[supertrend.index[sell_signals], 'High'],
                             mode='markers', marker=dict(symbol='triangle-down', size=10, color='red'),
                             name='Sell Signal'),
                  row=1, col=1)

    # Volume chart
    fig.add_trace(go.Bar(x=data.index, y=data['Volume'], name='Volume'), row=2, col=1)

    fig.update_layout(title=f'{ticker} SuperTrend Indicator',
                      xaxis_rangeslider_visible=False,
                      xaxis_title='Date',
                      yaxis_title='Price',
                      yaxis2_title='Volume')

    fig.show()

def main(ticker, start_date=None, end_date=None):
    if end_date is None:
        end_date = datetime.now()
    if start_date is None:
        start_date = end_date - timedelta(days=365)

    data = yf.download(ticker, start=start_date, end=end_date)

    # Calculate True Range
    data['TR'] = np.maximum(data['High'] - data['Low'],
                            np.maximum(abs(data['High'] - data['Close'].shift(1)),
                                       abs(data['Low'] - data['Close'].shift(1))))

    supertrend = calculate_supertrend(data)

    # Remove NaN values from the beginning of SuperTrend
    supertrend = supertrend.dropna()

    # Align data with SuperTrend
    data = data.loc[supertrend.index[0]:]

    plot_supertrend(data, supertrend, ticker)

    # Print last few rows to show current SuperTrend value and direction
    print(supertrend.tail())

if __name__ == "__main__":
    ticker = "SPY"  # SPDR S&P 500 ETF Trust
    main(ticker)