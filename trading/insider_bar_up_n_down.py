# https://www.youtube.com/watch?v=g6RZ4U44qMw&t=1896s

import pandas as pd
import yfinance as yf
from datetime import datetime, timedelta
import requests
from bs4 import BeautifulSoup
from tqdm import tqdm

def get_sp500_tickers():
    """Retrieve the current S&P 500 ticker list from Wikipedia."""
    url = "https://en.wikipedia.org/wiki/List_of_S%26P_500_companies"
    response = requests.get(url)
    soup = BeautifulSoup(response.text, 'html.parser')
    table = soup.find('table', {'class': 'wikitable sortable'})
    tickers = [row.find_all('td')[0].text.strip() for row in table.find_all('tr')[1:]]
    return tickers

def calculate_position_size(entry_price, stop_loss, risk_per_trade=100):
    risk_per_share = entry_price - stop_loss
    num_shares = risk_per_trade / risk_per_share
    return round(num_shares)

def check_inside_bar_conditions(df, lookback=3, volume_threshold=100000, close_threshold=100):
    """Check for inside bar conditions in the given dataframe."""
    df_len = len(df)
    for i in range(df_len - 1, 0, -1):
        current_day = df.iloc[i]
        previous_day = df.iloc[i-1]

        condition1 = current_day['High'] < previous_day['High']
        condition2 = current_day['Low'] > previous_day['Low']
        condition3 = current_day['Volume'] > volume_threshold
        condition4 = current_day['Close'] > close_threshold

        if condition1 and condition2 and condition3 and condition4:
            for j in range(i+1, min(i+lookback+1, df_len)):
                if df.iloc[j]['Close'] > current_day['High'] or df.iloc[j]['Close'] < current_day['Low']:
                    entry_price = current_day['Close']
                    stop_loss = previous_day['Low']
                    target_price = entry_price + 3 * (entry_price - stop_loss)  # 1:3 risk-reward ratio
                    num_shares = calculate_position_size(entry_price, stop_loss)
                    entry_cost = entry_price * num_shares
                    exit_price = target_price * num_shares
                    estimated_profit = exit_price - entry_cost
                    estimated_loss = entry_cost - (stop_loss * num_shares)
                    return True, df_len - (i - 1), round(entry_price, 2), round(stop_loss, 2), round(target_price, 2), num_shares, round(entry_cost, 2), round(exit_price, 2), round(estimated_profit, 2), round(estimated_loss, 2)

    return False, -1, '', '', '', '', '', '', '', ''

def main():
    # Parameters
    lookback = 5
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)  # Fetch 30 days of data

    # Get S&P 500 tickers
    sp500_tickers = get_sp500_tickers()

    # Initialize results list
    results = []

    # Create progress bar
    pbar = tqdm(sp500_tickers, desc="Screening stocks")

    # Screen stocks
    for ticker in pbar:
        try:
            # Fetch data
            stock = yf.Ticker(ticker.replace(".", "-"))
            df = stock.history(start=start_date, end=end_date)

            # Check conditions
            passed, position, entry_price, stop_loss, target_price, num_shares, entry_cost, exit_price, estimated_profit, estimated_loss = check_inside_bar_conditions(df, lookback)

            if passed:
                results.append((ticker, position, entry_price, stop_loss, target_price, num_shares, entry_cost, exit_price, estimated_profit, estimated_loss))
                pbar.set_description(f"Found: {ticker} at position {position}")

        except Exception as e:
            print(f"Error processing {ticker}: {str(e)}")

    # Print results
    print(f"\nStocks passing the screen: {len(results)}")
    for ticker, position, entry_price, stop_loss, target_price, num_shares, entry_cost, exit_price, estimated_profit, estimated_loss in results:
        print(f"{ticker} (Position: {position}) entry_price: {entry_price}, stop_loss: {stop_loss}, target_price: {target_price}, num_shares: {num_shares}, entry_cost: {entry_cost}, exit_price: {exit_price}, estimated_profit: {estimated_profit}, estimated_loss: {estimated_loss}")

    # Save results to file
    timestamp = datetime.now().strftime("%Y-%m-%dT%H-%M-%S")
    filename = f"insider-bar-hits-{timestamp}.txt"
    detailFilename = f"insider-bar-hits-details-{timestamp}.txt"
    with open(filename, 'w') as f:
        for ticker, position, entry_price, stop_loss, target_price, num_shares, entry_cost, exit_price, estimated_profit, estimated_loss in results:
            if (position <= 5):
                f.write(f"{ticker}\n")

    with open(detailFilename, 'w') as f:
        def sortSecond(val):
            return val[1]
        results.sort(key=sortSecond)
        for ticker, position, entry_price, stop_loss, target_price, num_shares, entry_cost, exit_price, estimated_profit, estimated_loss in results:
            f.write(f"{ticker} (Position: {position}) entry_cost: {entry_cost}, estimated_loss: {estimated_loss}, entry_price: {entry_price}, stop_loss: {stop_loss}, target_price: {target_price}, num_shares: {num_shares}, exit_price: {exit_price}, estimated_profit: {estimated_profit}\n")

    print(f"\nResults saved to {filename}")

if __name__ == "__main__":
    main()