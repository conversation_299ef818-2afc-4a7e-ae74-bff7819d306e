import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def calculate_supertrend(data, period=10, multiplier=3):
    hl2 = (data['High'] + data['Low']) / 2
    atr = data['TR'].rolling(period).mean()

    upper_basic = hl2 + (multiplier * atr)
    lower_basic = hl2 - (multiplier * atr)

    upper_band = pd.Series(index=data.index)
    lower_band = pd.Series(index=data.index)
    supertrend = pd.Series(index=data.index)
    direction = pd.Series(index=data.index)

    for i in range(period, len(data)):
        if i == period:
            upper_band.iloc[i] = upper_basic.iloc[i]
            lower_band.iloc[i] = lower_basic.iloc[i]
            supertrend.iloc[i] = upper_band.iloc[i] if data['Close'].iloc[i] <= upper_band.iloc[i] else lower_band.iloc[i]
            direction.iloc[i] = 1 if supertrend.iloc[i] == lower_band.iloc[i] else -1
        else:
            upper_band.iloc[i] = upper_basic.iloc[i] if (upper_basic.iloc[i] < upper_band.iloc[i-1] or data['Close'].iloc[i-1] > upper_band.iloc[i-1]) else upper_band.iloc[i-1]
            lower_band.iloc[i] = lower_basic.iloc[i] if (lower_basic.iloc[i] > lower_band.iloc[i-1] or data['Close'].iloc[i-1] < lower_band.iloc[i-1]) else lower_band.iloc[i-1]

            if supertrend.iloc[i-1] == upper_band.iloc[i-1]:
                supertrend.iloc[i] = upper_band.iloc[i] if data['Close'].iloc[i] <= upper_band.iloc[i] else lower_band.iloc[i]
            else:
                supertrend.iloc[i] = lower_band.iloc[i] if data['Close'].iloc[i] >= lower_band.iloc[i] else upper_band.iloc[i]

            direction.iloc[i] = 1 if supertrend.iloc[i] == lower_band.iloc[i] else -1

    return pd.DataFrame({'SuperTrend': supertrend, 'Direction': direction}, index=data.index)

def backtest_supertrend(data, supertrend):
    position = 0
    entry_price = 0
    trades = []

    for i in range(1, len(data)):
        if supertrend['Direction'].iloc[i] == 1 and supertrend['Direction'].iloc[i-1] == -1:
            # Buy signal
            if position == 0:
                position = 1
                entry_price = data['Close'].iloc[i]
        elif supertrend['Direction'].iloc[i] == -1 and supertrend['Direction'].iloc[i-1] == 1:
            # Sell signal
            if position == 1:
                exit_price = data['Close'].iloc[i]
                profit = (exit_price - entry_price) / entry_price
                trades.append(profit)
                position = 0

    # Close any open position at the end
    if position == 1:
        exit_price = data['Close'].iloc[-1]
        profit = (exit_price - entry_price) / entry_price
        trades.append(profit)

    return trades

def main(ticker, start_date=None, end_date=None):
    if end_date is None:
        end_date = datetime.now()
    if start_date is None:
        start_date = end_date - timedelta(days=365 * 5)  # 5 years of data

    data = yf.download(ticker, start=start_date, end=end_date)

    # Calculate True Range
    data['TR'] = np.maximum(data['High'] - data['Low'],
                            np.maximum(abs(data['High'] - data['Close'].shift(1)),
                                       abs(data['Low'] - data['Close'].shift(1))))

    supertrend = calculate_supertrend(data)
    trades = backtest_supertrend(data, supertrend)

    total_return = np.prod([1 + t for t in trades]) - 1
    num_trades = len(trades)
    win_rate = sum(1 for t in trades if t > 0) / num_trades if num_trades > 0 else 0

    print(f"Ticker: {ticker}")
    print(f"Total Return: {total_return:.2%}")
    print(f"Number of Trades: {num_trades}")
    print(f"Win Rate: {win_rate:.2%}")

if __name__ == "__main__":
    # ticker = "QQQ"  # Invesco QQQ Trust ETF
    ticker = "SPY"  # Invesco QQQ Trust ETF
    main(ticker)