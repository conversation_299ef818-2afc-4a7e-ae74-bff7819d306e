# fairValueGap.py
import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from tqdm import tqdm
import os
import warnings

warnings.filterwarnings('ignore', category=FutureWarning)

globalPeriod = '4h'
# ticker_file_path = 'data/stock_tickers.txt'
ticker_file_path = 'data/snp500_tickers.txt'

def get_stock_list():
    """Read stock tickers from a file."""
    stock_list = []
    try:
        with open(ticker_file_path, 'r') as file:
            stock_list = [line.strip().rstrip(',') for line in file if line.strip()]
    except FileNotFoundError:
        print(f"{ticker_file_path} not found.")
    return stock_list

def get_output_filename():
    """Generate unique filenames based on current script name and timestamp."""
    script_name = os.path.splitext(os.path.basename(__file__))[0]
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    scannerResults_directory = f"{os.path.expanduser('~')}/Downloads/trading/scannerResults"
    os.makedirs(scannerResults_directory, exist_ok=True)
    return [
        f"{scannerResults_directory}/{script_name}_{globalPeriod}_{timestamp}.txt",
        f"{scannerResults_directory}/{script_name}_{globalPeriod}_details_{timestamp}.txt"
    ]

def fetch_custom_data(symbol, period='1h'):
    end_date = datetime.now()

    match period:
        case '15m':
            start_date = end_date - timedelta(days=600)  # Get 600 days of data to have enough history
            data = yf.download(symbol, start=start_date, end=end_date, interval='1m', progress=False)
            data_1m = data.resample('15m').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            })
            return data_1m.dropna()
        case '1h':
            start_date = end_date - timedelta(days=600)  # Get 600 days of data to have enough history
            # Fetch 1-hour data and resample to 1-hour timeframe
            data = yf.download(symbol, start=start_date, end=end_date, interval='1h', progress=False)
            data_1h = data.resample('1h').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            })
            return data_1h.dropna()
        case '4h':
            start_date = end_date - timedelta(days=600)  # Get 600 days of data to have enough history
            # Fetch 1-hour data and resample to 4-hour timeframe
            data = yf.download(symbol, start=start_date, end=end_date, interval='1h', progress=False)
            # print(f'resamped: {data.resample('4h')}')
            data_4h = data.resample('4h').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            })
            # print(f"==>> data_4h: {data_4h.iloc[0].name}")
            return data_4h.dropna()
        case 'W':
            start_date = end_date - timedelta(days=1826)  # Get 5 years of data to have enough history
            # Fetch 1-hour data and resample to 4-hour timeframe
            data = yf.download(symbol, start=start_date, end=end_date, interval='1d', progress=False)
            data_weekly = data.resample('W').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            })
            return data_weekly.dropna()
        case 'ME':
            start_date = end_date - timedelta(days=1826)  # Get 5 years of data to have enough history
            # Fetch 1-hour data and resample to 4-hour timeframe
            data = yf.download(symbol, start=start_date, end=end_date, interval='1d', progress=False)
            data_monthly = data.resample('ME').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            })
            return data_monthly.dropna()
        case _:
            start_date = end_date - timedelta(days=1826)
            return yf.download(symbol, start=start_date, end=end_date, progress=False)

def identify_fvg(data):
    """Identify Fair Value Gaps and potential bounces."""
    df = data.copy()

    # Lists to store FVGs
    bullish_fvgs = []
    bearish_fvgs = []

    # Identify FVGs
    for i in range(1, len(df)-1):
        # Bullish FVG (gap up)
        if df['Low'].iloc[i+1] > df['High'].iloc[i-1]:
            fvg = {
                'type': 'bullish',
                'start_time': df.index[i-1],
                'gap_low': df['High'].iloc[i-1],
                'gap_high': df['Low'].iloc[i+1],
                'gap_mid': (df['High'].iloc[i-1] + df['Low'].iloc[i+1]) / 2
            }
            bullish_fvgs.append(fvg)

        # Bearish FVG (gap down)
        if df['High'].iloc[i+1] < df['Low'].iloc[i-1]:
            fvg = {
                'type': 'bearish',
                'start_time': df.index[i-1],
                'gap_high': df['Low'].iloc[i-1],
                'gap_low': df['High'].iloc[i+1],
                'gap_mid': (df['Low'].iloc[i-1] + df['High'].iloc[i+1]) / 2
            }
            bearish_fvgs.append(fvg)

    return bullish_fvgs, bearish_fvgs

def check_fvg_bounce(df, fvgs, last_candles=3):
    """Check if price has recently bounced from any FVG."""
    bounces = []
    recent_price = df.iloc[-last_candles:]

    for fvg in fvgs:
        if fvg['type'] == 'bullish':
            # Check if price came down to the FVG zone and bounced up
            if (recent_price['Low'].min() <= fvg['gap_high'] and
                recent_price['Low'].min() >= fvg['gap_low'] and
                recent_price['Close'].iloc[-1] > recent_price['Open'].iloc[-1]):
                bounces.append({
                    'type': 'bullish_bounce',
                    'fvg': fvg,
                    'bounce_price': recent_price['Low'].min()
                })

        else:  # bearish FVG
            # Check if price went up to the FVG zone and bounced down
            if (recent_price['High'].max() >= fvg['gap_low'] and
                recent_price['High'].max() <= fvg['gap_high'] and
                recent_price['Close'].iloc[-1] < recent_price['Open'].iloc[-1]):
                bounces.append({
                    'type': 'bearish_bounce',
                    'fvg': fvg,
                    'bounce_price': recent_price['High'].max()
                })

    return bounces

def calculate_position_size(entry_price, stop_loss, risk_per_trade=100):
    """Calculate position size based on risk management parameters."""
    risk_per_share = abs(entry_price - stop_loss)
    num_shares = risk_per_trade / risk_per_share
    return round(num_shares)

def check_fvg_strategy(symbol):
    """Check for FVG bounce signals for a given symbol."""
    try:
        data = fetch_custom_data(symbol.replace('.', '-'), period=globalPeriod)
        if len(data) < 24:  # Ensure we have enough data points
            return None

        bullish_fvgs, bearish_fvgs = identify_fvg(data)
        bounces = check_fvg_bounce(data, bullish_fvgs + bearish_fvgs)

        if bounces:
            latest_bounce = bounces[-1]
            current_price = data['Close'].iloc[-1]

            # Set up trade parameters based on bounce type
            if latest_bounce['type'] == 'bullish_bounce':
                entry_price = current_price
                stop_loss = latest_bounce['bounce_price'] * 0.99  # 1% below bounce
                target_price = entry_price + (entry_price - stop_loss) * 2  # 2:1 reward-risk
            else:
                entry_price = current_price
                stop_loss = latest_bounce['bounce_price'] * 1.01  # 1% above bounce
                target_price = entry_price - (stop_loss - entry_price) * 2  # 2:1 reward-risk

            # Calculate position size and costs
            num_shares = calculate_position_size(entry_price, stop_loss)
            entry_cost = entry_price * num_shares
            exit_price = target_price * num_shares
            estimated_profit = abs(exit_price - entry_cost)
            estimated_loss = abs(entry_cost - (stop_loss * num_shares))

            return (
                symbol,
                data.index[-1].strftime('%Y-%m-%d %H:%M'),
                f"FVG {latest_bounce['type']}",
                round(entry_price, 2),
                round(stop_loss, 2),
                round(target_price, 2),
                num_shares,
                round(entry_cost, 2),
                round(exit_price, 2),
                round(estimated_profit, 2),
                round(estimated_loss, 2),
                data['Volume'].iloc[-1]
            )

    except Exception as e:
        print(f"Error processing {symbol}: {str(e)}")

    return None

def main():
    symbols = get_stock_list()
    results = []

    print("Scanning for FVG bounce signals...")
    for symbol in tqdm(symbols):
        result = check_fvg_strategy(symbol)
        if result:
            results.append(result)

    if results:
        print("\nStocks with FVG bounce signals:")
        for (symbol, date, signal_type, entry_price, stop_loss, target_price,
             num_shares, entry_cost, exit_price, estimated_profit,
             estimated_loss, volume) in results:
            print(f"{symbol}: {signal_type} on {date}")
            print(f"Entry: ${entry_price}, Stop: ${stop_loss}, Target: ${target_price}")
            print(f"Shares: {num_shares}, Entry Cost: ${entry_cost}")
            print(f"Est. Profit: ${estimated_profit}, Est. Loss: ${estimated_loss}")
            print(f"Volume: {volume}\n")

        # Save results to files
        output_filenames = get_output_filename()
        with open(output_filenames[0], 'w') as f:
            for data in results:
                f.write(f"{data[0]}\n")

        with open(output_filenames[1], 'w') as f:
            for data in results:
                f.write(f"{data[0]}: {data[2]} on {data[1]}\n")
                f.write(f"Entry: ${data[3]}, Stop: ${data[4]}, Target: ${data[5]}\n")
                f.write(f"Shares: {data[6]}, Entry Cost: ${data[7]}\n")
                f.write(f"Est. Profit: ${data[9]}, Est. Loss: ${data[10]}\n")
                f.write(f"Volume: {data[11]}\n\n")

        print(f"\nResults saved to {output_filenames}")
    else:
        print("\nNo FVG bounce signals found.")

if __name__ == "__main__":
    main()