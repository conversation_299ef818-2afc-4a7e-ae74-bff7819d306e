import yfinance as yf
import pandas as pd
import numpy as np
from tqdm import tqdm
from datetime import datetime, timedelta
import os

def get_output_filename():
    """Generate a unique filename based on the current script name and timestamp."""
    script_name = os.path.splitext(os.path.basename(__file__))[0]
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return [f"{script_name}_{timestamp}.txt", f"{script_name}_details_{timestamp}.txt"]

def supertrend(data, factor=3, period=22):
    # Calculate True Range
    data['TR'] = np.maximum(data['High'] - data['Low'],
                            np.maximum(abs(data['High'] - data['Close'].shift(1)),
                                       abs(data['Low'] - data['Close'].shift(1))))

    hl2 = (data['High'] + data['Low']) / 2
    atr = data['TR'].rolling(period).mean()

    upper_basic = hl2 + (factor * atr)
    lower_basic = hl2 - (factor * atr)

    upper_band = pd.Series(index=data.index)
    lower_band = pd.Series(index=data.index)
    supertrendSeries = pd.Series(index=data.index)
    direction = pd.Series(index=data.index)

    for i in range(period, len(data)):
        if i == period:
            upper_band.iloc[i] = upper_basic.iloc[i]
            lower_band.iloc[i] = lower_basic.iloc[i]
            supertrendSeries.iloc[i] = upper_band.iloc[i] if data['Close'].iloc[i] <= upper_band.iloc[i] else lower_band.iloc[i]
            direction.iloc[i] = 1 if supertrendSeries.iloc[i] == lower_band.iloc[i] else -1
        else:
            upper_band.iloc[i] = upper_basic.iloc[i] if (upper_basic.iloc[i] < upper_band.iloc[i-1] or data['Close'].iloc[i-1] > upper_band.iloc[i-1]) else upper_band.iloc[i-1]
            lower_band.iloc[i] = lower_basic.iloc[i] if (lower_basic.iloc[i] > lower_band.iloc[i-1] or data['Close'].iloc[i-1] < lower_band.iloc[i-1]) else lower_band.iloc[i-1]

            if supertrendSeries.iloc[i-1] == upper_band.iloc[i-1]:
                supertrendSeries.iloc[i] = upper_band.iloc[i] if data['Close'].iloc[i] <= upper_band.iloc[i] else lower_band.iloc[i]
            else:
                supertrendSeries.iloc[i] = lower_band.iloc[i] if data['Close'].iloc[i] >= lower_band.iloc[i] else upper_band.iloc[i]

            direction.iloc[i] = 1 if supertrendSeries.iloc[i] == lower_band.iloc[i] else -1

    return pd.DataFrame({'SuperTrendSeries': supertrendSeries, 'Direction': direction}, index=data.index)

def get_sp500_symbols():
    url = "https://en.wikipedia.org/wiki/List_of_S%26P_500_companies"
    tables = pd.read_html(url)
    df = tables[0]
    return df['Symbol'].tolist()

def screen_stocks():
    symbols = get_sp500_symbols() + ['SPY', 'QQQ']
    results = []

    end_date = datetime.today()
    start_date = end_date - timedelta(days=365)

    for symbol in tqdm(symbols, desc="Screening stocks"):
        try:
            stock = yf.Ticker(symbol.replace(".", "-"))
            data = stock.history(start=start_date, end=end_date)

            if len(data) < 7:
                continue

            trend = supertrend(data)

            last_week = trend['Direction'].tail(7)
            if 1 in last_week.values and -1 in last_week.values:
                reversal_index = None
                reversal_date = None
                reversal_type = None

                if last_week.iloc[-1] == 1 and -1 in last_week.iloc[:-1].values:
                    reversal_type = "Bullish Reversal"
                    reversal_index = last_week[last_week == 1].index[0]
                elif last_week.iloc[-1] == -1 and 1 in last_week.iloc[:-1].values:
                    reversal_type = "Bearish Reversal"
                    reversal_index = last_week[last_week == -1].index[0]

                if reversal_index is not None:
                    reversal_date = reversal_index.date()
                    # Find the integer position of the reversal
                    reversal_position = trend.index.get_loc(reversal_index)
                    if isinstance(reversal_position, slice):
                        reversal_position = reversal_position.start
                    results.append((symbol, reversal_type, reversal_position, reversal_date))

        except Exception as e:
            print(f"Error processing {symbol}: {str(e)}")

    return results

if __name__ == "__main__":
    results = screen_stocks()

    if (len(results) > 0):
        # Save results to a dynamically named file
        output_filenames = get_output_filename()
        with open(output_filenames[0], 'w') as f:
            for symbol, reversal_type, reversal_index, reversal_date in results:
                f.write(f"{symbol}\n")

        with open(output_filenames[1], 'w') as f:
            print("\nStocks with SuperTrend reversals in the last week:")
            for symbol, reversal_type, reversal_index, reversal_date in results:
                print(f"{symbol}: {reversal_type} at index {reversal_index}, date {reversal_date}")
                f.write(f"{symbol}: {reversal_type} at index {reversal_index}, date {reversal_date}\n")

        print(f"\nResults saved to {output_filenames}")
