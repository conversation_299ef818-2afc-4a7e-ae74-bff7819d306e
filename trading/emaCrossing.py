# emaCrossing.py
import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from tqdm import tqdm
import os
import warnings

# Option 1: Suppress the warning
warnings.filterwarnings('ignore', category=FutureWarning)

globalPeriod = '1h'
# ticker_file_path = 'data/stock_tickers.txt'
ticker_file_path = 'data/snp500_tickers.txt'

def get_stock_list():
    """Read stock tickers from a file."""
    stock_list = []
    try:
        with open(ticker_file_path, 'r') as file:
            stock_list = [line.strip().rstrip(',') for line in file if line.strip()]
    except FileNotFoundError:
        print(f"{ticker_file_path} not found.")
    return stock_list

def get_output_filename():
    """Generate unique filenames based on current script name and timestamp."""
    script_name = os.path.splitext(os.path.basename(__file__))[0]
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    scannerResults_directory = f"{os.path.expanduser('~')}/Downloads/trading/scannerResults"
    os.makedirs(scannerResults_directory, exist_ok=True)
    return [
        f"{scannerResults_directory}/{script_name}_{globalPeriod}_{timestamp}.txt",
        f"{scannerResults_directory}/{script_name}_{globalPeriod}_details_{timestamp}.txt"
    ]

def fetch_custom_data(symbol, period='1h'):
    end_date = datetime.now()

    match period:
        case '15m':
            start_date = end_date - timedelta(days=600)  # Get 600 days of data to have enough history
            data = yf.download(symbol, start=start_date, end=end_date, interval='1m', progress=False)
            data_1m = data.resample('15m').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            })
            return data_1m.dropna()
        case '1h':
            start_date = end_date - timedelta(days=600)  # Get 600 days of data to have enough history
            # Fetch 1-hour data and resample to 1-hour timeframe
            data = yf.download(symbol, start=start_date, end=end_date, interval='1h', progress=False)
            data_1h = data.resample('1h').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            })
            return data_1h.dropna()
        case '4h':
            start_date = end_date - timedelta(days=600)  # Get 600 days of data to have enough history
            # Fetch 1-hour data and resample to 4-hour timeframe
            data = yf.download(symbol, start=start_date, end=end_date, interval='1h', progress=False)
            # print(f'resamped: {data.resample('4h')}')
            data_4h = data.resample('4h').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            })
            # print(f"==>> data_4h: {data_4h.iloc[0].name}")
            return data_4h.dropna()
        case 'W':
            start_date = end_date - timedelta(days=1826)  # Get 5 years of data to have enough history
            # Fetch 1-hour data and resample to 4-hour timeframe
            data = yf.download(symbol, start=start_date, end=end_date, interval='1d', progress=False)
            data_weekly = data.resample('W').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            })
            return data_weekly.dropna()
        case 'ME':
            start_date = end_date - timedelta(days=1826)  # Get 5 years of data to have enough history
            # Fetch 1-hour data and resample to 4-hour timeframe
            data = yf.download(symbol, start=start_date, end=end_date, interval='1d', progress=False)
            data_monthly = data.resample('ME').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            })
            return data_monthly.dropna()
        case _:
            start_date = end_date - timedelta(days=1826)
            return yf.download(symbol, start=start_date, end=end_date, progress=False)

def calculate_ema_signals(data):
    """Calculate EMAs and detect crossover signals."""
    df = data.copy()

    # Calculate EMAs
    df['EMA8'] = df['Close'].ewm(span=8, adjust=False).mean()
    df['EMA13'] = df['Close'].ewm(span=13, adjust=False).mean()
    df['EMA21'] = df['Close'].ewm(span=21, adjust=False).mean()
    df['EMA55'] = df['Close'].ewm(span=55, adjust=False).mean()

    # Detect buy signal (EMA55 crosses below all other EMAs)
    df['buy_signal'] = (
        (df['EMA55'] < df['EMA8']) &
        (df['EMA55'] < df['EMA13']) &
        (df['EMA55'] < df['EMA21'])
    ).astype(bool)  # Ensure boolean type

    # Detect previous candle's conditions to find new signals
    df['prev_buy_signal'] = df['buy_signal'].shift(1)
    # Handle NaN values and ensure boolean type
    df['prev_buy_signal'] = df['prev_buy_signal'].fillna(False).astype(bool)
    df['new_buy_signal'] = (df['buy_signal'] & ~df['prev_buy_signal']).astype(bool)

    return df

def calculate_position_size(entry_price, stop_loss, risk_per_trade=100):
    """Calculate position size based on risk management parameters."""
    risk_per_share = abs(entry_price - stop_loss)
    num_shares = risk_per_trade / risk_per_share
    return round(num_shares)

def check_ema_strategy(symbol):
    """Check for EMA strategy signals for a given symbol."""
    try:
        data = fetch_custom_data(symbol.replace('.', '-'), period=globalPeriod)
        if len(data) < 60:  # Ensure we have enough data points for EMAs
            return None

        data = calculate_ema_signals(data)

        # Check if we have a new buy signal in the last 2 days
        last_candles = data.iloc[-2:]
        if last_candles['new_buy_signal'].any():
            entry_price = round(last_candles['Close'].iloc[-1], 2)

            # Set stop loss below the recent low
            stop_loss = round(data['Low'].tail(5).min() * 0.99, 2)  # 1% below recent low

            # Set target using a 3:1 reward-to-risk ratio
            risk = entry_price - stop_loss
            target_price = round(entry_price + (3 * risk), 2)

            # Calculate position size
            num_shares = calculate_position_size(entry_price, stop_loss)

            # Calculate entry cost and potential profit/loss
            entry_cost = entry_price * num_shares
            exit_price = target_price * num_shares
            estimated_profit = abs(exit_price - entry_cost)
            estimated_loss = abs(entry_cost - (stop_loss * num_shares))

            return (
                symbol,
                last_candles.index[-1].strftime('%Y-%m-%d'),
                "EMA Buy Signal",
                entry_price,
                stop_loss,
                target_price,
                num_shares,
                round(entry_cost, 2),
                round(exit_price, 2),
                round(estimated_profit, 2),
                round(estimated_loss, 2),
                last_candles['Volume'].sum()
            )

    except Exception as e:
        print(f"Error processing {symbol}: {str(e)}")

    return None

def main():
    symbols = get_stock_list()
    results = []

    print("Scanning for EMA strategy signals...")
    for symbol in tqdm(symbols):
        result = check_ema_strategy(symbol)
        if result:
            results.append(result)

    if results:
        print("\nStocks with EMA strategy signals:")
        for (symbol, date, signal_type, entry_price, stop_loss, target_price,
             num_shares, entry_cost, exit_price, estimated_profit,
             estimated_loss, volume) in results:
            print(f"{symbol}: {signal_type} on {date}")
            print(f"Entry: ${entry_price}, Stop: ${stop_loss}, Target: ${target_price}")
            print(f"Shares: {num_shares}, Entry Cost: ${entry_cost}")
            print(f"Est. Profit: ${estimated_profit}, Est. Loss: ${estimated_loss}")
            print(f"Volume: {volume}\n")

        # Save results to files
        output_filenames = get_output_filename()
        with open(output_filenames[0], 'w') as f:
            for data in results:
                f.write(f"{data[0]}\n")

        with open(output_filenames[1], 'w') as f:
            for data in results:
                f.write(f"{data[0]}: {data[2]} on {data[1]}\n")
                f.write(f"Entry: ${data[3]}, Stop: ${data[4]}, Target: ${data[5]}\n")
                f.write(f"Shares: {data[6]}, Entry Cost: ${data[7]}\n")
                f.write(f"Est. Profit: ${data[9]}, Est. Loss: ${data[10]}\n")
                f.write(f"Volume: {data[11]}\n\n")

        print(f"\nResults saved to {output_filenames}")
    else:
        print("\nNo EMA strategy signals found.")

if __name__ == "__main__":
    main()